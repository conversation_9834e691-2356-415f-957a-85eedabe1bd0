# 多模型配置示例
# 支持各种兼容OpenAI接口的大模型

# OpenAI官方模型
openai_config:
  provider: "openai"
  model: "gpt-3.5-turbo"  # 或 gpt-4, gpt-4-turbo
  temperature: 0.1
  max_tokens: 1000
  # API配置
  api_key: "${OPENAI_API_KEY}"  # 从环境变量读取
  base_url: "https://api.openai.com/v1"

# Anthropic Claude模型
anthropic_config:
  provider: "anthropic"
  model: "claude-3-sonnet-20240229"  # 或 claude-3-opus-20240229, claude-3-haiku-20240307
  temperature: 0.1
  max_tokens: 1000
  api_key: "${ANTHROPIC_API_KEY}"

# Azure OpenAI模型
azure_openai_config:
  provider: "azure_openai"
  model: "gpt-35-turbo"  # Azure中的模型名称
  deployment_name: "gpt-35-turbo"  # Azure部署名称
  api_version: "2023-12-01-preview"
  temperature: 0.1
  max_tokens: 1000
  api_key: "${AZURE_OPENAI_API_KEY}"
  endpoint: "${AZURE_OPENAI_ENDPOINT}"

# 本地Ollama模型
ollama_config:
  provider: "ollama"
  model: "llama2"  # 或 llama3, mistral, codellama等
  base_url: "http://localhost:11434"
  temperature: 0.1

# 阿里云通义千问（兼容OpenAI接口）
qwen_config:
  provider: "openai"
  model: "qwen-turbo"  # 或 qwen-plus, qwen-max
  temperature: 0.1
  max_tokens: 1000
  api_key: "${DASHSCOPE_API_KEY}"
  base_url: "https://dashscope.aliyuncs.com/compatible-mode/v1"

# 智谱AI GLM模型（兼容OpenAI接口）
zhipu_config:
  provider: "openai"
  model: "glm-4"  # 或 glm-3-turbo
  temperature: 0.1
  max_tokens: 1000
  api_key: "${ZHIPUAI_API_KEY}"
  base_url: "https://open.bigmodel.cn/api/paas/v4/"

# 百度文心一言（兼容OpenAI接口）
ernie_config:
  provider: "openai"
  model: "ernie-bot-turbo"  # 或 ernie-bot, ernie-bot-4
  temperature: 0.1
  max_tokens: 1000
  api_key: "${QIANFAN_ACCESS_KEY}"
  base_url: "https://aip.baidubce.com/rpc/2.0/ai_custom/v1/wenxinworkshop/chat/"

# 腾讯混元模型（兼容OpenAI接口）
hunyuan_config:
  provider: "openai"
  model: "hunyuan-lite"  # 或 hunyuan-standard, hunyuan-pro
  temperature: 0.1
  max_tokens: 1000
  api_key: "${HUNYUAN_API_KEY}"
  base_url: "https://hunyuan.tencentcloudapi.com/v1"

# 字节跳动豆包模型（兼容OpenAI接口）
doubao_config:
  provider: "openai"
  model: "doubao-lite-4k"  # 或 doubao-pro-4k, doubao-pro-32k
  temperature: 0.1
  max_tokens: 1000
  api_key: "${VOLCENGINE_API_KEY}"
  base_url: "https://ark.cn-beijing.volces.com/api/v3"

# 月之暗面Kimi模型（兼容OpenAI接口）
kimi_config:
  provider: "openai"
  model: "moonshot-v1-8k"  # 或 moonshot-v1-32k, moonshot-v1-128k
  temperature: 0.1
  max_tokens: 1000
  api_key: "${MOONSHOT_API_KEY}"
  base_url: "https://api.moonshot.cn/v1"

# 零一万物Yi模型（兼容OpenAI接口）
yi_config:
  provider: "openai"
  model: "yi-34b-chat-0205"  # 或 yi-34b-chat-200k
  temperature: 0.1
  max_tokens: 1000
  api_key: "${YI_API_KEY}"
  base_url: "https://api.lingyiwanwu.com/v1"

# DeepSeek模型（兼容OpenAI接口）
deepseek_config:
  provider: "openai"
  model: "deepseek-chat"  # 或 deepseek-coder
  temperature: 0.1
  max_tokens: 1000
  api_key: "${DEEPSEEK_API_KEY}"
  base_url: "https://api.deepseek.com/v1"

# 本地部署的模型（如vLLM、FastChat等）
local_vllm_config:
  provider: "openai"
  model: "vicuna-7b-v1.5"  # 本地部署的模型名称
  temperature: 0.1
  max_tokens: 1000
  api_key: "EMPTY"  # 本地部署通常不需要API key
  base_url: "http://localhost:8000/v1"

# 使用代理的配置示例
proxy_config:
  provider: "openai"
  model: "gpt-3.5-turbo"
  temperature: 0.1
  max_tokens: 1000
  api_key: "${OPENAI_API_KEY}"
  base_url: "https://your-proxy-server.com/v1"
  # 可以添加额外的HTTP配置
  timeout: 30
  max_retries: 3

# 完整的系统配置示例
complete_system_config:
  llm:
    # 文档处理器使用OpenAI
    document_processor:
      provider: "openai"
      model: "gpt-3.5-turbo"
      temperature: 0.1
      max_tokens: 1000
      chunk_size: 1000
      chunk_overlap: 200
    
    # 查询处理器使用通义千问
    query_processor:
      provider: "openai"
      model: "qwen-turbo"
      temperature: 0.1
      max_tokens: 1000
      api_key: "${DASHSCOPE_API_KEY}"
      base_url: "https://dashscope.aliyuncs.com/compatible-mode/v1"
    
    # 结果优化器使用Claude
    result_optimizer:
      provider: "anthropic"
      model: "claude-3-sonnet-20240229"
      temperature: 0.1
      max_tokens: 1500
      relevance_weight: 0.6
      quality_weight: 0.4
      api_key: "${ANTHROPIC_API_KEY}"
    
    # 功能开关
    enable_query_enhancement: true
    enable_result_optimization: true
    enable_answer_generation: true

# 环境变量配置说明
environment_variables:
  # OpenAI
  OPENAI_API_KEY: "your-openai-api-key"
  OPENAI_API_BASE: "https://api.openai.com/v1"  # 可选
  
  # Anthropic
  ANTHROPIC_API_KEY: "your-anthropic-api-key"
  
  # Azure OpenAI
  AZURE_OPENAI_API_KEY: "your-azure-openai-key"
  AZURE_OPENAI_ENDPOINT: "https://your-resource.openai.azure.com"
  
  # 国产模型API Keys
  DASHSCOPE_API_KEY: "your-qwen-api-key"
  ZHIPUAI_API_KEY: "your-zhipu-api-key"
  QIANFAN_ACCESS_KEY: "your-baidu-api-key"
  HUNYUAN_API_KEY: "your-tencent-api-key"
  VOLCENGINE_API_KEY: "your-bytedance-api-key"
  MOONSHOT_API_KEY: "your-kimi-api-key"
  YI_API_KEY: "your-yi-api-key"
  DEEPSEEK_API_KEY: "your-deepseek-api-key"

# 模型选择建议
model_recommendations:
  # 高质量任务推荐
  high_quality:
    - "gpt-4"
    - "claude-3-opus-20240229"
    - "qwen-max"
  
  # 平衡性能和成本
  balanced:
    - "gpt-3.5-turbo"
    - "claude-3-sonnet-20240229"
    - "qwen-turbo"
    - "glm-4"
  
  # 成本优先
  cost_effective:
    - "qwen-turbo"
    - "glm-3-turbo"
    - "ernie-bot-turbo"
    - "doubao-lite-4k"
  
  # 本地部署
  local_deployment:
    - "llama2"
    - "llama3"
    - "mistral"
    - "vicuna"

# 注意事项
notes:
  - "确保设置正确的环境变量"
  - "不同模型的API格式可能略有差异"
  - "注意各模型的token限制和计费方式"
  - "本地模型需要先启动对应的服务"
  - "代理配置需要确保网络连通性"
