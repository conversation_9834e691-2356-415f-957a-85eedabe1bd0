# 多模型支持指南

本系统支持接入多种大模型，包括 OpenAI、Anthropic、以及各种兼容 OpenAI 接口的模型。

## 🎯 支持的模型提供商

### 1. 官方API提供商

| 提供商 | 模型示例 | 环境变量 | 说明 |
|--------|----------|----------|------|
| OpenAI | gpt-3.5-turbo, gpt-4 | `OPENAI_API_KEY` | 官方OpenAI模型 |
| Anthropic | claude-3-sonnet | `ANTHROPIC_API_KEY` | Claude系列模型 |
| Azure OpenAI | gpt-35-turbo | `AZURE_OPENAI_API_KEY` | 微软Azure平台 |

### 2. 兼容OpenAI接口的国产模型

| 提供商 | 模型示例 | 环境变量 | 接口地址 |
|--------|----------|----------|----------|
| 阿里云通义千问 | qwen-turbo, qwen-plus | `DASHSCOPE_API_KEY` | dashscope.aliyuncs.com |
| 智谱AI | glm-4, glm-3-turbo | `ZHIPUAI_API_KEY` | open.bigmodel.cn |
| 百度文心一言 | ernie-bot-turbo | `QIANFAN_ACCESS_KEY` | aip.baidubce.com |
| 月之暗面Kimi | moonshot-v1-8k | `MOONSHOT_API_KEY` | api.moonshot.cn |
| DeepSeek | deepseek-chat | `DEEPSEEK_API_KEY` | api.deepseek.com |
| 零一万物Yi | yi-34b-chat | `YI_API_KEY` | api.lingyiwanwu.com |

### 3. 本地部署模型

| 方案 | 模型示例 | 说明 |
|------|----------|------|
| Ollama | llama2, llama3, mistral | 本地部署开源模型 |
| vLLM | 各种开源模型 | 高性能推理服务 |
| FastChat | vicuna, alpaca | 对话模型服务 |

## 🚀 快速开始

### 1. 环境变量设置

```bash
# OpenAI
export OPENAI_API_KEY="your-openai-api-key"

# 通义千问
export DASHSCOPE_API_KEY="your-qwen-api-key"

# 智谱AI
export ZHIPUAI_API_KEY="your-zhipu-api-key"

# 其他模型...
```

### 2. 配置文件设置

编辑 `config/search_config.yaml`：

```yaml
llm:
  # 查询处理器使用通义千问
  query_processor:
    provider: "openai"
    model: "qwen-turbo"
    temperature: 0.1
    max_tokens: 1000
    base_url: "https://dashscope.aliyuncs.com/compatible-mode/v1"
  
  # 结果优化器使用智谱AI
  result_optimizer:
    provider: "openai"
    model: "glm-4"
    temperature: 0.1
    max_tokens: 1500
    base_url: "https://open.bigmodel.cn/api/paas/v4/"
```

### 3. 代码中使用

```python
from src.llm.query_processor import LLMQueryProcessor

# 使用通义千问
config = {
    'provider': 'openai',
    'model': 'qwen-turbo',
    'temperature': 0.1,
    'max_tokens': 1000,
    'base_url': 'https://dashscope.aliyuncs.com/compatible-mode/v1'
}

processor = LLMQueryProcessor(config)
result = await processor.analyze_query("人工智能的应用")
```

## 🔧 模型切换工具

### 使用ModelSwitcher

```python
from src.utils.model_switcher import ModelSwitcher

# 创建切换器
switcher = ModelSwitcher()

# 查看当前配置
switcher.print_current_status()

# 切换到通义千问
qwen_config = switcher.create_model_config('qwen', 'qwen-turbo')
switcher.switch_all_models(qwen_config)
switcher.save_config()

# 快速预设切换
switcher.quick_switch('qwen_turbo')
switcher.save_config()
```

### 可用的快速预设

- `openai_gpt35`: OpenAI GPT-3.5
- `openai_gpt4`: OpenAI GPT-4
- `claude_sonnet`: Anthropic Claude-3-Sonnet
- `qwen_turbo`: 阿里云通义千问
- `zhipu_glm4`: 智谱AI GLM-4
- `local_llama`: 本地Llama模型

## 📋 配置参数说明

### 通用参数

| 参数 | 说明 | 默认值 |
|------|------|--------|
| `provider` | 模型提供商 | "openai" |
| `model` | 模型名称 | "gpt-3.5-turbo" |
| `temperature` | 温度参数 | 0.1 |
| `max_tokens` | 最大token数 | 1000 |

### 特定提供商参数

#### OpenAI兼容接口
```yaml
provider: "openai"
model: "qwen-turbo"
api_key: "your-api-key"  # 可选，优先使用环境变量
base_url: "https://api.example.com/v1"
```

#### Azure OpenAI
```yaml
provider: "azure_openai"
model: "gpt-35-turbo"
deployment_name: "gpt-35-turbo"
api_version: "2023-12-01-preview"
endpoint: "https://your-resource.openai.azure.com"
```

#### Ollama本地模型
```yaml
provider: "ollama"
model: "llama2"
base_url: "http://localhost:11434"
```

## 🎨 使用场景建议

### 1. 高质量任务
- **推荐模型**: GPT-4, Claude-3-Opus, 通义千问Max
- **适用场景**: 复杂推理、创意写作、代码生成

### 2. 平衡性能和成本
- **推荐模型**: GPT-3.5-turbo, Claude-3-Sonnet, 通义千问Turbo
- **适用场景**: 日常查询处理、文档分析

### 3. 成本优先
- **推荐模型**: 通义千问Turbo, GLM-3-turbo, 文心一言Turbo
- **适用场景**: 大批量处理、实时响应

### 4. 本地部署
- **推荐模型**: Llama2/3, Mistral, CodeLlama
- **适用场景**: 数据安全要求高、离线环境

## 🔍 模型性能对比

| 模型 | 响应速度 | 质量评分 | 成本 | 中文支持 |
|------|----------|----------|------|----------|
| GPT-4 | ⭐⭐⭐ | ⭐⭐⭐⭐⭐ | 💰💰💰 | ⭐⭐⭐⭐ |
| GPT-3.5-turbo | ⭐⭐⭐⭐ | ⭐⭐⭐⭐ | 💰💰 | ⭐⭐⭐⭐ |
| Claude-3-Sonnet | ⭐⭐⭐ | ⭐⭐⭐⭐⭐ | 💰💰💰 | ⭐⭐⭐ |
| 通义千问Turbo | ⭐⭐⭐⭐⭐ | ⭐⭐⭐⭐ | 💰 | ⭐⭐⭐⭐⭐ |
| GLM-4 | ⭐⭐⭐⭐ | ⭐⭐⭐⭐ | 💰💰 | ⭐⭐⭐⭐⭐ |
| 本地Llama | ⭐⭐ | ⭐⭐⭐ | 免费 | ⭐⭐ |

## 🛠️ 故障排除

### 常见问题

1. **API密钥错误**
   ```
   解决方案: 检查环境变量设置，确保API密钥正确
   ```

2. **网络连接问题**
   ```
   解决方案: 检查网络连接，考虑使用代理
   ```

3. **模型不支持**
   ```
   解决方案: 查看模型提供商文档，确认模型名称正确
   ```

4. **token限制**
   ```
   解决方案: 调整max_tokens参数或使用支持更长上下文的模型
   ```

### 调试技巧

```python
# 启用详细日志
import logging
logging.basicConfig(level=logging.DEBUG)

# 测试模型连接
from src.llm.model_adapter import create_model_adapter

config = {
    'provider': 'openai',
    'model': 'qwen-turbo',
    'base_url': 'https://dashscope.aliyuncs.com/compatible-mode/v1'
}

try:
    adapter = create_model_adapter(config)
    response = await adapter.ainvoke("测试连接")
    print("连接成功:", response)
except Exception as e:
    print("连接失败:", e)
```

## 📚 更多资源

- [模型配置示例](../config/multi_model_examples.yaml)
- [使用演示](../examples/multi_model_demo.py)
- [API参考文档](API_REFERENCE.md)
- [性能优化指南](PERFORMANCE_OPTIMIZATION.md)

## 🤝 贡献

欢迎贡献新的模型适配器！请参考现有适配器的实现方式，并确保：

1. 实现 `BaseModelAdapter` 接口
2. 添加相应的配置示例
3. 更新文档说明
4. 提供测试用例
