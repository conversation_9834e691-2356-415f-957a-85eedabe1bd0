"""
多阶段检索策略
实现召回率优化和精确重排序的两阶段检索流程
"""
import logging
import asyncio
from typing import List, Dict, Any, Optional, Tuple
from dataclasses import dataclass

from ..core.interfaces import SearchStrategy, DataSource, SearchQuery, SearchResult, SearchType
from ..llm.reranker_models import BaseReranker, BGEReranker, CrossEncoderReranker, ColBERTReranker, CohereReranker, RerankResult
from .hybrid_strategy import HybridSearchStrategy
from .keyword_strategy import KeywordSearchStrategy
from .semantic_strategy import SemanticSearchStrategy

logger = logging.getLogger(__name__)


@dataclass
class MultiStageConfig:
    """多阶段检索配置"""
    # 第一阶段：召回配置
    recall_limit: int = 100  # 召回候选数量
    recall_strategies: List[str] = None  # 召回策略列表
    enable_query_expansion: bool = True  # 启用查询扩展
    enable_negative_sampling: bool = True  # 启用负采样
    
    # 第二阶段：重排序配置
    reranker_type: str = 'bge'  # reranker类型
    reranker_config: Dict[str, Any] = None  # reranker配置
    final_limit: int = 10  # 最终返回数量
    
    # 融合配置
    fusion_method: str = 'weighted'  # 融合方法
    recall_weight: float = 0.3  # 召回分数权重
    rerank_weight: float = 0.7  # 重排序分数权重


class MultiStageSearchStrategy(SearchStrategy):
    """多阶段检索策略"""
    
    def __init__(self, config: Dict[str, Any]):
        super().__init__(config)
        
        # 解析配置
        self.stage_config = MultiStageConfig(
            recall_limit=config.get('recall_limit', 100),
            recall_strategies=config.get('recall_strategies', ['keyword', 'semantic']),
            enable_query_expansion=config.get('enable_query_expansion', True),
            enable_negative_sampling=config.get('enable_negative_sampling', True),
            reranker_type=config.get('reranker_type', 'bge'),
            reranker_config=config.get('reranker_config', {}),
            final_limit=config.get('final_limit', 10),
            fusion_method=config.get('fusion_method', 'weighted'),
            recall_weight=config.get('recall_weight', 0.3),
            rerank_weight=config.get('rerank_weight', 0.7)
        )
        
        # 初始化召回策略
        self.recall_strategies = self._initialize_recall_strategies()
        
        # 初始化重排序器
        self.reranker = self._initialize_reranker()
        
        # 查询扩展器
        self.query_expander = None
        if self.stage_config.enable_query_expansion:
            from ..llm.query_processor import LLMQueryProcessor
            self.query_expander = LLMQueryProcessor(config.get('query_processor', {}))
    
    def _initialize_recall_strategies(self) -> Dict[str, SearchStrategy]:
        """初始化召回策略"""
        strategies = {}
        
        for strategy_name in self.stage_config.recall_strategies:
            if strategy_name == 'keyword':
                strategies['keyword'] = KeywordSearchStrategy(
                    self.config.get('keyword_config', {})
                )
            elif strategy_name == 'semantic':
                strategies['semantic'] = SemanticSearchStrategy(
                    self.config.get('semantic_config', {})
                )
            elif strategy_name == 'hybrid':
                strategies['hybrid'] = HybridSearchStrategy(
                    self.config.get('hybrid_config', {})
                )
        
        return strategies
    
    def _initialize_reranker(self) -> BaseReranker:
        """初始化重排序器"""
        reranker_type = self.stage_config.reranker_type.lower()
        reranker_config = self.stage_config.reranker_config or {}
        
        if reranker_type == 'bge':
            return BGEReranker(reranker_config)
        elif reranker_type == 'cross_encoder':
            return CrossEncoderReranker(reranker_config)
        elif reranker_type == 'colbert':
            return ColBERTReranker(reranker_config)
        elif reranker_type == 'cohere':
            return CohereReranker(reranker_config)
        else:
            logger.warning(f"Unknown reranker type: {reranker_type}, using BGE")
            return BGEReranker(reranker_config)
    
    async def search(self, data_sources: List[DataSource], query: SearchQuery) -> List[SearchResult]:
        """执行多阶段检索"""
        try:
            # 第一阶段：召回
            recall_results = await self._stage1_recall(data_sources, query)
            
            if not recall_results:
                return []
            
            # 第二阶段：重排序
            reranked_results = await self._stage2_rerank(query, recall_results)
            
            # 返回最终结果
            final_results = [result.original_result for result in reranked_results[:self.stage_config.final_limit]]
            
            # 更新分数
            for i, (final_result, rerank_result) in enumerate(zip(final_results, reranked_results)):
                final_result.score = self._compute_final_score(
                    final_result.score, 
                    rerank_result.rerank_score
                )
                final_result.metadata.update({
                    'original_rank': rerank_result.original_rank,
                    'rerank_score': rerank_result.rerank_score,
                    'final_rank': i,
                    'reranker_type': self.stage_config.reranker_type
                })
            
            logger.info(f"Multi-stage search completed: {len(recall_results)} -> {len(final_results)}")
            return final_results
            
        except Exception as e:
            logger.error(f"Multi-stage search failed: {e}")
            return []
    
    async def _stage1_recall(self, data_sources: List[DataSource], query: SearchQuery) -> List[SearchResult]:
        """第一阶段：召回"""
        all_results = []
        
        # 创建召回查询
        recall_query = SearchQuery(
            query=query.query,
            search_type=query.search_type,
            filters=query.filters,
            limit=self.stage_config.recall_limit,
            offset=query.offset,
            boost_fields=query.boost_fields,
            min_score=0.0  # 降低分数阈值以获得更多候选
        )
        
        # 执行多种召回策略
        recall_tasks = []
        for strategy_name, strategy in self.recall_strategies.items():
            if strategy_name == 'semantic' and hasattr(strategy, 'initialize'):
                await strategy.initialize()
            
            task = strategy.search(data_sources, recall_query)
            recall_tasks.append((strategy_name, task))
        
        # 并行执行召回
        recall_results = await asyncio.gather(
            *[task for _, task in recall_tasks], 
            return_exceptions=True
        )
        
        # 合并召回结果
        for (strategy_name, _), results in zip(recall_tasks, recall_results):
            if isinstance(results, Exception):
                logger.error(f"Recall strategy {strategy_name} failed: {results}")
                continue
            
            # 添加策略标识
            for result in results:
                result.metadata['recall_strategy'] = strategy_name
            
            all_results.extend(results)
        
        # 查询扩展召回
        if self.stage_config.enable_query_expansion and self.query_expander:
            expanded_results = await self._expanded_recall(data_sources, query)
            all_results.extend(expanded_results)
        
        # 去重和初步排序
        deduplicated_results = self._deduplicate_results(all_results)
        
        # 负采样优化
        if self.stage_config.enable_negative_sampling:
            optimized_results = await self._negative_sampling(deduplicated_results, query)
            return optimized_results[:self.stage_config.recall_limit]
        
        return deduplicated_results[:self.stage_config.recall_limit]
    
    async def _expanded_recall(self, data_sources: List[DataSource], query: SearchQuery) -> List[SearchResult]:
        """查询扩展召回"""
        try:
            # 分析查询并获取扩展查询
            query_analysis = await self.query_expander.analyze_query(query.query)
            
            expanded_results = []
            for expanded_query in query_analysis.expanded_queries[:3]:  # 限制扩展查询数量
                if expanded_query != query.query:
                    expanded_search_query = SearchQuery(
                        query=expanded_query,
                        search_type=query.search_type,
                        filters=query.filters,
                        limit=self.stage_config.recall_limit // 3,
                        offset=query.offset,
                        boost_fields=query.boost_fields,
                        min_score=0.0
                    )
                    
                    # 使用主要召回策略
                    main_strategy = list(self.recall_strategies.values())[0]
                    results = await main_strategy.search(data_sources, expanded_search_query)
                    
                    # 标记为扩展查询结果
                    for result in results:
                        result.metadata['expanded_query'] = expanded_query
                        result.score *= 0.8  # 降低扩展查询结果的权重
                    
                    expanded_results.extend(results)
            
            return expanded_results
            
        except Exception as e:
            logger.error(f"Expanded recall failed: {e}")
            return []
    
    async def _negative_sampling(self, results: List[SearchResult], query: SearchQuery) -> List[SearchResult]:
        """负采样优化"""
        try:
            # 简单的负采样：移除明显不相关的结果
            filtered_results = []
            
            for result in results:
                # 基于分数和内容长度的简单过滤
                if result.score > 0.1 and len(result.content.strip()) > 10:
                    filtered_results.append(result)
            
            return filtered_results
            
        except Exception as e:
            logger.error(f"Negative sampling failed: {e}")
            return results
    
    async def _stage2_rerank(self, query: SearchQuery, results: List[SearchResult]) -> List[RerankResult]:
        """第二阶段：重排序"""
        try:
            # 初始化重排序器
            if not self.reranker.initialized:
                await self.reranker.initialize()
            
            # 执行重排序
            reranked_results = await self.reranker.rerank(query.query, results)
            
            logger.info(f"Reranking completed with {self.stage_config.reranker_type}")
            return reranked_results
            
        except Exception as e:
            logger.error(f"Reranking failed: {e}")
            # 返回原始排序
            return [
                RerankResult(
                    original_result=result,
                    rerank_score=result.score,
                    original_rank=i,
                    new_rank=i,
                    explanation="Reranking failed, using original score"
                ) for i, result in enumerate(results)
            ]
    
    def _compute_final_score(self, recall_score: float, rerank_score: float) -> float:
        """计算最终分数"""
        if self.stage_config.fusion_method == 'weighted':
            return (recall_score * self.stage_config.recall_weight + 
                   rerank_score * self.stage_config.rerank_weight)
        elif self.stage_config.fusion_method == 'max':
            return max(recall_score, rerank_score)
        elif self.stage_config.fusion_method == 'rerank_only':
            return rerank_score
        else:
            return rerank_score
    
    def _deduplicate_results(self, results: List[SearchResult]) -> List[SearchResult]:
        """去重结果"""
        seen = set()
        deduplicated = []
        
        for result in results:
            key = f"{result.id}_{result.source}"
            if key not in seen:
                seen.add(key)
                deduplicated.append(result)
        
        # 按分数排序
        deduplicated.sort(key=lambda x: x.score, reverse=True)
        return deduplicated
    
    def merge_results(self, results_list: List[List[SearchResult]]) -> List[SearchResult]:
        """合并多个数据源的检索结果"""
        all_results = []
        for results in results_list:
            all_results.extend(results)
        
        return self._deduplicate_results(all_results)
