# 性能优化和最佳实践指南

## 概述

本指南详细介绍了如何优化大模型增强检索系统的性能，包括响应时间、吞吐量、资源利用率和成本控制等方面的最佳实践。

## 性能指标基准

### 目标性能指标

| 指标 | 目标值 | 说明 |
|------|--------|------|
| 平均响应时间 | < 2秒 | 端到端搜索响应时间 |
| P95响应时间 | < 5秒 | 95%请求的响应时间 |
| 并发处理能力 | 100+ QPS | 每秒查询处理数 |
| 系统可用性 | 99.9% | 系统正常运行时间 |
| 内存使用率 | < 80% | 系统内存占用率 |
| CPU使用率 | < 70% | 系统CPU占用率 |

### 性能测试

创建性能测试脚本 `scripts/performance_test.py`：

```python
import asyncio
import time
import statistics
from concurrent.futures import ThreadPoolExecutor
from src.llm.enhanced_search_manager import EnhancedSearchManager
from src.core.interfaces import SearchQuery, SearchType

async def single_search_test(search_manager, query_text):
    """单次搜索测试"""
    start_time = time.time()
    
    query = SearchQuery(
        query=query_text,
        search_type=SearchType.HYBRID,
        limit=5
    )
    
    try:
        response = await search_manager.enhanced_search(query)
        end_time = time.time()
        return {
            'success': True,
            'duration': end_time - start_time,
            'results_count': response['total_results']
        }
    except Exception as e:
        end_time = time.time()
        return {
            'success': False,
            'duration': end_time - start_time,
            'error': str(e)
        }

async def concurrent_test(search_manager, queries, concurrency=10):
    """并发测试"""
    semaphore = asyncio.Semaphore(concurrency)
    
    async def limited_search(query):
        async with semaphore:
            return await single_search_test(search_manager, query)
    
    tasks = [limited_search(query) for query in queries]
    results = await asyncio.gather(*tasks)
    
    # 统计结果
    successful_results = [r for r in results if r['success']]
    failed_results = [r for r in results if not r['success']]
    
    if successful_results:
        durations = [r['duration'] for r in successful_results]
        stats = {
            'total_requests': len(results),
            'successful_requests': len(successful_results),
            'failed_requests': len(failed_results),
            'success_rate': len(successful_results) / len(results) * 100,
            'avg_duration': statistics.mean(durations),
            'p50_duration': statistics.median(durations),
            'p95_duration': statistics.quantiles(durations, n=20)[18] if len(durations) > 20 else max(durations),
            'min_duration': min(durations),
            'max_duration': max(durations)
        }
    else:
        stats = {
            'total_requests': len(results),
            'successful_requests': 0,
            'failed_requests': len(failed_results),
            'success_rate': 0
        }
    
    return stats

# 运行测试
async def run_performance_test():
    # 测试查询
    test_queries = [
        "人工智能的应用领域",
        "机器学习算法比较",
        "深度学习入门指南",
        "Python数据分析",
        "自然语言处理技术",
        "计算机视觉应用",
        "推荐系统原理",
        "大数据处理技术"
    ] * 10  # 重复查询增加测试量
    
    search_manager = EnhancedSearchManager(config)
    await search_manager.initialize()
    
    print("开始性能测试...")
    
    # 并发测试
    for concurrency in [1, 5, 10, 20]:
        print(f"\n并发度: {concurrency}")
        stats = await concurrent_test(search_manager, test_queries[:20], concurrency)
        
        print(f"总请求数: {stats['total_requests']}")
        print(f"成功率: {stats['success_rate']:.1f}%")
        if stats['successful_requests'] > 0:
            print(f"平均响应时间: {stats['avg_duration']:.2f}s")
            print(f"P95响应时间: {stats['p95_duration']:.2f}s")
            print(f"QPS: {stats['successful_requests'] / stats['max_duration']:.1f}")
    
    await search_manager.cleanup()

if __name__ == "__main__":
    asyncio.run(run_performance_test())
```

## LLM API 优化

### 1. 请求优化

**批处理策略**：
```python
class BatchProcessor:
    def __init__(self, batch_size=5, max_wait_time=1.0):
        self.batch_size = batch_size
        self.max_wait_time = max_wait_time
        self.pending_requests = []
        self.batch_timer = None
    
    async def add_request(self, request):
        self.pending_requests.append(request)
        
        if len(self.pending_requests) >= self.batch_size:
            await self._process_batch()
        elif self.batch_timer is None:
            self.batch_timer = asyncio.create_task(
                self._wait_and_process()
            )
    
    async def _wait_and_process(self):
        await asyncio.sleep(self.max_wait_time)
        if self.pending_requests:
            await self._process_batch()
    
    async def _process_batch(self):
        if not self.pending_requests:
            return
        
        batch = self.pending_requests.copy()
        self.pending_requests.clear()
        self.batch_timer = None
        
        # 批量处理请求
        await self._execute_batch(batch)
```

**Token 优化**：
```python
def optimize_prompt(self, content: str, max_tokens: int = 2000) -> str:
    """优化提示内容，控制token数量"""
    if self._count_tokens(content) <= max_tokens:
        return content
    
    # 智能截取策略
    sentences = content.split('。')
    optimized_content = ""
    
    for sentence in sentences:
        test_content = optimized_content + sentence + "。"
        if self._count_tokens(test_content) > max_tokens:
            break
        optimized_content = test_content
    
    return optimized_content

def _count_tokens(self, text: str) -> int:
    """计算token数量"""
    return len(self.encoding.encode(text))
```

**并发控制**：
```python
class RateLimitedLLM:
    def __init__(self, llm, max_concurrent=5, requests_per_minute=60):
        self.llm = llm
        self.semaphore = asyncio.Semaphore(max_concurrent)
        self.rate_limiter = AsyncRateLimiter(requests_per_minute)
    
    async def ainvoke(self, prompt):
        async with self.semaphore:
            await self.rate_limiter.acquire()
            return await self.llm.ainvoke(prompt)

class AsyncRateLimiter:
    def __init__(self, requests_per_minute):
        self.requests_per_minute = requests_per_minute
        self.requests = []
    
    async def acquire(self):
        now = time.time()
        # 清理过期请求
        self.requests = [req_time for req_time in self.requests 
                        if now - req_time < 60]
        
        if len(self.requests) >= self.requests_per_minute:
            sleep_time = 60 - (now - self.requests[0])
            if sleep_time > 0:
                await asyncio.sleep(sleep_time)
        
        self.requests.append(now)
```

### 2. 缓存策略

**多级缓存**：
```python
class MultiLevelCache:
    def __init__(self):
        self.memory_cache = {}  # 内存缓存
        self.redis_cache = None  # Redis缓存
        self.memory_ttl = 300   # 5分钟
        self.redis_ttl = 3600   # 1小时
    
    async def get(self, key):
        # 1. 检查内存缓存
        if key in self.memory_cache:
            data, timestamp = self.memory_cache[key]
            if time.time() - timestamp < self.memory_ttl:
                return data
            else:
                del self.memory_cache[key]
        
        # 2. 检查Redis缓存
        if self.redis_cache:
            data = await self.redis_cache.get(key)
            if data:
                # 回填内存缓存
                self.memory_cache[key] = (data, time.time())
                return data
        
        return None
    
    async def set(self, key, value):
        # 设置内存缓存
        self.memory_cache[key] = (value, time.time())
        
        # 设置Redis缓存
        if self.redis_cache:
            await self.redis_cache.setex(key, self.redis_ttl, value)
```

**智能缓存键**：
```python
def generate_cache_key(self, query: str, config: dict) -> str:
    """生成缓存键"""
    # 标准化查询
    normalized_query = query.lower().strip()
    
    # 包含配置参数
    config_hash = hashlib.md5(
        json.dumps(config, sort_keys=True).encode()
    ).hexdigest()[:8]
    
    # 生成键
    query_hash = hashlib.md5(normalized_query.encode()).hexdigest()[:16]
    return f"search:{query_hash}:{config_hash}"
```

## 向量计算优化

### 1. FAISS 优化

**索引选择**：
```python
def create_optimized_index(self, dimension: int, num_vectors: int):
    """创建优化的FAISS索引"""
    if num_vectors < 1000:
        # 小数据集使用平面索引
        return faiss.IndexFlatIP(dimension)
    elif num_vectors < 100000:
        # 中等数据集使用IVF索引
        nlist = min(int(math.sqrt(num_vectors)), 1000)
        quantizer = faiss.IndexFlatIP(dimension)
        return faiss.IndexIVFFlat(quantizer, dimension, nlist)
    else:
        # 大数据集使用HNSW索引
        return faiss.IndexHNSWFlat(dimension, 32)
```

**GPU 加速**：
```python
def setup_gpu_index(self, cpu_index):
    """设置GPU索引"""
    if faiss.get_num_gpus() > 0:
        res = faiss.StandardGpuResources()
        # 配置GPU内存
        res.setTempMemory(1024 * 1024 * 1024)  # 1GB
        
        # 转换为GPU索引
        gpu_index = faiss.index_cpu_to_gpu(res, 0, cpu_index)
        return gpu_index
    return cpu_index
```

### 2. 向量预计算

**预计算查询向量**：
```python
class QueryVectorCache:
    def __init__(self, model, cache_size=1000):
        self.model = model
        self.cache = {}
        self.cache_size = cache_size
        self.access_times = {}
    
    async def get_query_vector(self, query: str):
        if query in self.cache:
            self.access_times[query] = time.time()
            return self.cache[query]
        
        # 计算向量
        vector = self.model.encode([query])[0]
        
        # 缓存管理
        if len(self.cache) >= self.cache_size:
            # 移除最久未使用的项
            oldest_query = min(self.access_times.keys(), 
                             key=lambda k: self.access_times[k])
            del self.cache[oldest_query]
            del self.access_times[oldest_query]
        
        self.cache[query] = vector
        self.access_times[query] = time.time()
        return vector
```

## 数据库优化

### 1. 连接池优化

```python
from sqlalchemy.pool import QueuePool

# PostgreSQL 连接池配置
engine = create_async_engine(
    database_url,
    poolclass=QueuePool,
    pool_size=20,          # 连接池大小
    max_overflow=30,       # 最大溢出连接
    pool_timeout=30,       # 获取连接超时
    pool_recycle=3600,     # 连接回收时间
    pool_pre_ping=True     # 连接预检查
)
```

### 2. 查询优化

**索引策略**：
```sql
-- 文本搜索索引
CREATE INDEX CONCURRENTLY idx_documents_content_gin 
ON documents USING gin(to_tsvector('english', content));

-- 复合索引
CREATE INDEX CONCURRENTLY idx_documents_category_date 
ON documents(category, created_at DESC);

-- 部分索引
CREATE INDEX CONCURRENTLY idx_documents_active 
ON documents(id) WHERE status = 'active';
```

**查询优化**：
```python
async def optimized_search(self, query: SearchQuery):
    """优化的数据库搜索"""
    # 使用预编译语句
    stmt = select(Document).where(
        Document.content.match(query.query)
    ).limit(query.limit)
    
    # 添加查询提示
    if query.filters:
        for filter_condition in query.filters:
            if filter_condition.field == 'category':
                stmt = stmt.where(Document.category == filter_condition.value)
    
    # 执行查询
    result = await self.session.execute(stmt)
    return result.scalars().all()
```

### 3. Elasticsearch 优化

**映射优化**：
```json
{
  "mappings": {
    "properties": {
      "content": {
        "type": "text",
        "analyzer": "standard",
        "search_analyzer": "standard",
        "store": false
      },
      "content_vector": {
        "type": "dense_vector",
        "dims": 384,
        "index": true,
        "similarity": "cosine"
      },
      "category": {
        "type": "keyword"
      },
      "created_at": {
        "type": "date",
        "format": "strict_date_optional_time"
      }
    }
  },
  "settings": {
    "number_of_shards": 1,
    "number_of_replicas": 0,
    "refresh_interval": "30s",
    "index.max_result_window": 50000,
    "index.mapping.total_fields.limit": 2000
  }
}
```

**搜索优化**：
```python
def build_optimized_query(self, query: SearchQuery):
    """构建优化的ES查询"""
    es_query = {
        "query": {
            "bool": {
                "must": [],
                "should": [],
                "filter": []
            }
        },
        "_source": ["id", "content", "metadata"],  # 只返回需要的字段
        "highlight": {
            "fields": {
                "content": {
                    "fragment_size": 150,
                    "number_of_fragments": 3
                }
            }
        }
    }
    
    # 主查询
    if query.search_type == SearchType.KEYWORD:
        es_query["query"]["bool"]["must"].append({
            "multi_match": {
                "query": query.query,
                "fields": ["content^2", "title^3"],
                "type": "best_fields",
                "fuzziness": "AUTO"
            }
        })
    
    # 筛选条件
    for filter_condition in query.filters or []:
        es_query["query"]["bool"]["filter"].append({
            "term": {filter_condition.field: filter_condition.value}
        })
    
    return es_query
```

## 内存优化

### 1. 对象池

```python
class ObjectPool:
    def __init__(self, create_func, max_size=100):
        self.create_func = create_func
        self.max_size = max_size
        self.pool = []
        self.in_use = set()
    
    def acquire(self):
        if self.pool:
            obj = self.pool.pop()
        else:
            obj = self.create_func()
        
        self.in_use.add(id(obj))
        return obj
    
    def release(self, obj):
        obj_id = id(obj)
        if obj_id in self.in_use:
            self.in_use.remove(obj_id)
            if len(self.pool) < self.max_size:
                # 重置对象状态
                if hasattr(obj, 'reset'):
                    obj.reset()
                self.pool.append(obj)
```

### 2. 内存监控

```python
import psutil
import gc

class MemoryMonitor:
    def __init__(self, threshold=0.8):
        self.threshold = threshold
    
    def check_memory_usage(self):
        """检查内存使用情况"""
        memory = psutil.virtual_memory()
        if memory.percent / 100 > self.threshold:
            logger.warning(f"High memory usage: {memory.percent}%")
            self.cleanup_memory()
    
    def cleanup_memory(self):
        """清理内存"""
        # 强制垃圾回收
        gc.collect()
        
        # 清理缓存
        if hasattr(self, 'cache'):
            self.cache.clear()
        
        logger.info("Memory cleanup completed")
```

## 监控和调优

### 1. 性能监控

```python
import time
from functools import wraps

def monitor_performance(func):
    """性能监控装饰器"""
    @wraps(func)
    async def wrapper(*args, **kwargs):
        start_time = time.time()
        try:
            result = await func(*args, **kwargs)
            duration = time.time() - start_time
            
            # 记录性能指标
            logger.info(f"{func.__name__} completed in {duration:.2f}s")
            
            # 发送到监控系统
            if hasattr(wrapper, 'metrics'):
                wrapper.metrics.record_duration(func.__name__, duration)
            
            return result
        except Exception as e:
            duration = time.time() - start_time
            logger.error(f"{func.__name__} failed after {duration:.2f}s: {e}")
            raise
    
    return wrapper
```

### 2. 自动调优

```python
class AutoTuner:
    def __init__(self):
        self.performance_history = []
        self.current_config = {}
    
    def record_performance(self, config, metrics):
        """记录性能数据"""
        self.performance_history.append({
            'config': config.copy(),
            'metrics': metrics,
            'timestamp': time.time()
        })
        
        # 保持历史记录在合理范围内
        if len(self.performance_history) > 1000:
            self.performance_history = self.performance_history[-500:]
    
    def suggest_optimization(self):
        """建议优化配置"""
        if len(self.performance_history) < 10:
            return None
        
        # 分析最佳配置
        best_performance = min(self.performance_history, 
                             key=lambda x: x['metrics']['avg_duration'])
        
        return {
            'suggested_config': best_performance['config'],
            'expected_improvement': self._calculate_improvement(best_performance)
        }
```

## 成本优化

### 1. LLM API 成本控制

```python
class CostController:
    def __init__(self, daily_budget=100.0):
        self.daily_budget = daily_budget
        self.daily_usage = 0.0
        self.last_reset = datetime.now().date()
    
    def check_budget(self, estimated_cost):
        """检查预算"""
        today = datetime.now().date()
        if today != self.last_reset:
            self.daily_usage = 0.0
            self.last_reset = today
        
        if self.daily_usage + estimated_cost > self.daily_budget:
            raise BudgetExceededException(
                f"Daily budget exceeded: {self.daily_usage + estimated_cost} > {self.daily_budget}"
            )
        
        self.daily_usage += estimated_cost
    
    def estimate_cost(self, prompt, model="gpt-3.5-turbo"):
        """估算API调用成本"""
        token_count = self._count_tokens(prompt)
        
        # 价格表（每1K tokens）
        prices = {
            "gpt-3.5-turbo": 0.002,
            "gpt-4": 0.03
        }
        
        return (token_count / 1000) * prices.get(model, 0.002)
```

### 2. 智能降级策略

```python
class IntelligentFallback:
    def __init__(self):
        self.fallback_levels = [
            {'model': 'gpt-4', 'features': ['full']},
            {'model': 'gpt-3.5-turbo', 'features': ['basic']},
            {'model': 'local', 'features': ['minimal']}
        ]
    
    async def execute_with_fallback(self, operation, **kwargs):
        """带降级的执行"""
        for level in self.fallback_levels:
            try:
                return await self._execute_level(operation, level, **kwargs)
            except Exception as e:
                logger.warning(f"Level {level['model']} failed: {e}")
                continue
        
        raise Exception("All fallback levels failed")
    
    async def _execute_level(self, operation, level, **kwargs):
        """执行特定级别的操作"""
        if level['model'] == 'local':
            return await self._local_fallback(operation, **kwargs)
        else:
            return await operation(model=level['model'], **kwargs)
```

通过实施这些优化策略，系统可以在保持高质量服务的同时，显著提升性能和降低成本。
