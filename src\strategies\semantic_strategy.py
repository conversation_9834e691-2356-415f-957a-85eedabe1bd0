"""
语义检索策略实现
"""
import logging
import asyncio
from typing import List, Dict, Any
import numpy as np
from sentence_transformers import SentenceTransformer

from ..core.interfaces import SearchStrategy, DataSource, SearchQuery, SearchResult, SearchType

logger = logging.getLogger(__name__)


class SemanticSearchStrategy(SearchStrategy):
    """语义检索策略"""
    
    def __init__(self, config: Dict[str, Any]):
        super().__init__(config)
        self.model_name = config.get('model_name', 'all-MiniLM-L6-v2')
        self.model = None
        self.semantic_weight = config.get('semantic_weight', 1.0)
        self.similarity_threshold = config.get('similarity_threshold', 0.3)
        
    async def initialize(self):
        """初始化语义模型"""
        if not self.model:
            try:
                self.model = SentenceTransformer(self.model_name)
                logger.info(f"Loaded semantic model: {self.model_name}")
            except Exception as e:
                logger.error(f"Failed to load semantic model: {e}")
                raise
    
    async def search(self, data_sources: List[DataSource], query: SearchQuery) -> List[SearchResult]:
        """执行语义检索"""
        # 确保模型已加载
        await self.initialize()
        
        # 确保使用语义检索类型
        semantic_query = SearchQuery(
            query=query.query,
            search_type=SearchType.SEMANTIC,
            filters=query.filters,
            limit=query.limit * 2,  # 获取更多结果用于合并
            offset=query.offset,
            boost_fields=query.boost_fields,
            min_score=max(query.min_score, self.similarity_threshold)
        )
        
        # 并行搜索所有数据源
        tasks = []
        for data_source in data_sources:
            if await data_source.health_check():
                tasks.append(self._search_single_source(data_source, semantic_query))
            else:
                logger.warning(f"Data source {data_source.name} failed health check, skipping")
        
        if not tasks:
            return []
        
        # 等待所有搜索完成
        results_list = await asyncio.gather(*tasks, return_exceptions=True)
        
        # 过滤异常结果
        valid_results = []
        for result in results_list:
            if isinstance(result, Exception):
                logger.error(f"Semantic search failed: {result}")
            else:
                valid_results.append(result)
        
        # 合并结果
        merged_results = self.merge_results(valid_results)
        
        # 重新计算语义相似度分数
        enhanced_results = await self._enhance_semantic_scores(merged_results, query.query)
        
        # 返回指定数量的结果
        return enhanced_results[:query.limit]
    
    async def _search_single_source(self, data_source: DataSource, query: SearchQuery) -> List[SearchResult]:
        """搜索单个数据源"""
        try:
            results = await data_source.search(query)
            
            # 为非向量数据源的结果计算语义相似度
            if hasattr(data_source, 'source_type') and data_source.source_type != 'vector_db':
                results = await self._calculate_semantic_similarity(results, query.query)
            
            return results
            
        except Exception as e:
            logger.error(f"Semantic search failed for {data_source.name}: {e}")
            return []
    
    async def _calculate_semantic_similarity(self, results: List[SearchResult], query: str) -> List[SearchResult]:
        """为结果计算语义相似度"""
        if not results or not self.model:
            return results
        
        try:
            # 获取查询向量
            query_embedding = self.model.encode([query])
            
            # 获取文档向量
            contents = [result.content for result in results]
            doc_embeddings = self.model.encode(contents)
            
            # 计算相似度
            from sklearn.metrics.pairwise import cosine_similarity
            similarities = cosine_similarity(query_embedding, doc_embeddings)[0]
            
            # 更新结果分数
            enhanced_results = []
            for i, result in enumerate(results):
                semantic_score = float(similarities[i])
                
                # 过滤低相似度结果
                if semantic_score < self.similarity_threshold:
                    continue
                
                # 结合原始分数和语义分数
                combined_score = (result.score * 0.3 + semantic_score * 0.7) * self.semantic_weight
                
                enhanced_result = SearchResult(
                    id=result.id,
                    content=result.content,
                    score=combined_score,
                    metadata={**result.metadata, 'semantic_score': semantic_score},
                    source=result.source,
                    search_type=SearchType.SEMANTIC
                )
                enhanced_results.append(enhanced_result)
            
            return enhanced_results
            
        except Exception as e:
            logger.error(f"Failed to calculate semantic similarity: {e}")
            return results
    
    async def _enhance_semantic_scores(self, results: List[SearchResult], query: str) -> List[SearchResult]:
        """增强语义分数计算"""
        if not results or not self.model:
            return results
        
        try:
            # 对所有结果重新计算语义相似度以确保一致性
            query_embedding = self.model.encode([query])
            contents = [result.content for result in results]
            doc_embeddings = self.model.encode(contents)
            
            from sklearn.metrics.pairwise import cosine_similarity
            similarities = cosine_similarity(query_embedding, doc_embeddings)[0]
            
            enhanced_results = []
            for i, result in enumerate(results):
                semantic_score = float(similarities[i])
                
                if semantic_score < self.similarity_threshold:
                    continue
                
                # 更新分数
                result.score = semantic_score * self.semantic_weight
                result.metadata['semantic_score'] = semantic_score
                enhanced_results.append(result)
            
            # 按语义相似度排序
            enhanced_results.sort(key=lambda x: x.score, reverse=True)
            return enhanced_results
            
        except Exception as e:
            logger.error(f"Failed to enhance semantic scores: {e}")
            return results
    
    def merge_results(self, results_list: List[List[SearchResult]]) -> List[SearchResult]:
        """合并多个数据源的语义检索结果"""
        if not results_list:
            return []
        
        # 收集所有结果并去重
        result_map = {}
        
        for results in results_list:
            for result in results:
                key = f"{result.id}_{result.source}"
                if key in result_map:
                    # 对于语义检索，取平均分数
                    existing = result_map[key]
                    avg_score = (existing.score + result.score) / 2
                    existing.score = avg_score
                    existing.metadata.update(result.metadata)
                else:
                    result_map[key] = result
        
        # 转换为列表并按语义相似度排序
        merged_results = list(result_map.values())
        merged_results.sort(key=lambda x: x.score, reverse=True)
        
        return merged_results
