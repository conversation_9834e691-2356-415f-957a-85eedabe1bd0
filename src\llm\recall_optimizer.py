"""
召回率优化器
实现多种召回率提升策略
"""
import logging
import asyncio
import numpy as np
from typing import List, Dict, Any, Optional, Tuple, Set
from dataclasses import dataclass
from collections import defaultdict

from ..core.interfaces import SearchQuery, SearchResult, SearchType

logger = logging.getLogger(__name__)


@dataclass
class RecallMetrics:
    """召回率指标"""
    total_relevant: int
    retrieved_relevant: int
    total_retrieved: int
    recall: float
    precision: float
    f1_score: float


class RecallOptimizer:
    """召回率优化器"""
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        
        # 查询扩展配置
        self.enable_query_expansion = config.get('enable_query_expansion', True)
        self.max_expanded_queries = config.get('max_expanded_queries', 5)
        self.expansion_weight = config.get('expansion_weight', 0.8)
        
        # 多模型检索配置
        self.enable_multi_model = config.get('enable_multi_model', True)
        self.model_weights = config.get('model_weights', {})
        
        # 候选集扩展配置
        self.candidate_expansion_factor = config.get('candidate_expansion_factor', 3)
        self.min_candidate_score = config.get('min_candidate_score', 0.1)
        
        # 负采样配置
        self.enable_negative_sampling = config.get('enable_negative_sampling', True)
        self.negative_ratio = config.get('negative_ratio', 0.2)
        
        # 查询重写配置
        self.enable_query_rewriting = config.get('enable_query_rewriting', True)
        self.rewrite_strategies = config.get('rewrite_strategies', ['synonym', 'paraphrase'])
        
        # 初始化组件
        self.query_expander = None
        self.synonym_generator = None
        self.paraphrase_generator = None
        
    async def initialize(self):
        """初始化优化器"""
        try:
            if self.enable_query_expansion:
                from .query_processor import LLMQueryProcessor
                self.query_expander = LLMQueryProcessor(self.config.get('query_processor', {}))
                await self.query_expander.initialize()
            
            logger.info("Recall optimizer initialized successfully")
            
        except Exception as e:
            logger.error(f"Failed to initialize recall optimizer: {e}")
            raise
    
    async def optimize_recall(self, query: SearchQuery, 
                            search_function, 
                            target_recall: float = 0.8) -> List[SearchResult]:
        """优化召回率"""
        try:
            # 1. 基础检索
            base_results = await search_function(query)
            
            # 2. 查询扩展检索
            expanded_results = []
            if self.enable_query_expansion:
                expanded_results = await self._query_expansion_recall(query, search_function)
            
            # 3. 查询重写检索
            rewritten_results = []
            if self.enable_query_rewriting:
                rewritten_results = await self._query_rewriting_recall(query, search_function)
            
            # 4. 多模型检索
            multi_model_results = []
            if self.enable_multi_model:
                multi_model_results = await self._multi_model_recall(query, search_function)
            
            # 5. 合并和去重
            all_results = base_results + expanded_results + rewritten_results + multi_model_results
            deduplicated_results = self._deduplicate_results(all_results)
            
            # 6. 候选集扩展
            expanded_candidates = self._expand_candidate_set(deduplicated_results, query)
            
            # 7. 负采样过滤
            if self.enable_negative_sampling:
                filtered_results = await self._negative_sampling_filter(expanded_candidates, query)
            else:
                filtered_results = expanded_candidates
            
            # 8. 评估召回率
            recall_metrics = self._evaluate_recall(filtered_results, query)
            
            logger.info(f"Recall optimization completed: {recall_metrics}")
            return filtered_results
            
        except Exception as e:
            logger.error(f"Recall optimization failed: {e}")
            return await search_function(query)
    
    async def _query_expansion_recall(self, query: SearchQuery, search_function) -> List[SearchResult]:
        """查询扩展召回"""
        try:
            if not self.query_expander:
                return []
            
            # 分析查询并获取扩展
            query_analysis = await self.query_expander.analyze_query(query.query)
            
            expanded_results = []
            for expanded_query in query_analysis.expanded_queries[:self.max_expanded_queries]:
                if expanded_query != query.query:
                    # 创建扩展查询
                    expanded_search_query = SearchQuery(
                        query=expanded_query,
                        search_type=query.search_type,
                        filters=query.filters,
                        limit=query.limit,
                        offset=query.offset,
                        boost_fields=query.boost_fields,
                        min_score=self.min_candidate_score
                    )
                    
                    # 执行搜索
                    results = await search_function(expanded_search_query)
                    
                    # 调整分数权重
                    for result in results:
                        result.score *= self.expansion_weight
                        result.metadata['expansion_query'] = expanded_query
                        result.metadata['expansion_type'] = 'llm_expansion'
                    
                    expanded_results.extend(results)
            
            return expanded_results
            
        except Exception as e:
            logger.error(f"Query expansion recall failed: {e}")
            return []
    
    async def _query_rewriting_recall(self, query: SearchQuery, search_function) -> List[SearchResult]:
        """查询重写召回"""
        try:
            rewritten_results = []
            
            for strategy in self.rewrite_strategies:
                if strategy == 'synonym':
                    rewritten_queries = await self._generate_synonym_queries(query.query)
                elif strategy == 'paraphrase':
                    rewritten_queries = await self._generate_paraphrase_queries(query.query)
                else:
                    continue
                
                for rewritten_query in rewritten_queries:
                    if rewritten_query != query.query:
                        rewritten_search_query = SearchQuery(
                            query=rewritten_query,
                            search_type=query.search_type,
                            filters=query.filters,
                            limit=query.limit // 2,  # 减少每个重写查询的结果数
                            offset=query.offset,
                            boost_fields=query.boost_fields,
                            min_score=self.min_candidate_score
                        )
                        
                        results = await search_function(rewritten_search_query)
                        
                        # 标记重写类型
                        for result in results:
                            result.score *= 0.9  # 略微降低重写查询的权重
                            result.metadata['rewritten_query'] = rewritten_query
                            result.metadata['rewrite_strategy'] = strategy
                        
                        rewritten_results.extend(results)
            
            return rewritten_results
            
        except Exception as e:
            logger.error(f"Query rewriting recall failed: {e}")
            return []
    
    async def _generate_synonym_queries(self, query: str) -> List[str]:
        """生成同义词查询"""
        try:
            # 简单的同义词替换示例
            # 在实际应用中，可以使用WordNet、同义词词典或LLM
            synonym_map = {
                '机器学习': ['人工智能', 'AI', '深度学习'],
                '算法': ['方法', '技术', '模型'],
                '数据': ['信息', '资料', '数据集'],
                '分析': ['研究', '分析', '探索'],
                '应用': ['使用', '运用', '实践']
            }
            
            synonym_queries = []
            for original, synonyms in synonym_map.items():
                if original in query:
                    for synonym in synonyms:
                        synonym_query = query.replace(original, synonym)
                        if synonym_query != query:
                            synonym_queries.append(synonym_query)
            
            return synonym_queries[:3]  # 限制数量
            
        except Exception as e:
            logger.error(f"Synonym generation failed: {e}")
            return []
    
    async def _generate_paraphrase_queries(self, query: str) -> List[str]:
        """生成释义查询"""
        try:
            # 使用LLM生成释义
            if self.query_expander:
                # 这里可以调用LLM生成释义
                # 简化示例
                paraphrases = [
                    f"关于{query}的信息",
                    f"{query}相关内容",
                    f"有关{query}的资料"
                ]
                return paraphrases[:2]
            
            return []
            
        except Exception as e:
            logger.error(f"Paraphrase generation failed: {e}")
            return []
    
    async def _multi_model_recall(self, query: SearchQuery, search_function) -> List[SearchResult]:
        """多模型召回"""
        try:
            # 使用不同的搜索类型
            multi_model_results = []
            
            search_types = [SearchType.KEYWORD, SearchType.SEMANTIC, SearchType.HYBRID]
            
            for search_type in search_types:
                if search_type != query.search_type:
                    multi_model_query = SearchQuery(
                        query=query.query,
                        search_type=search_type,
                        filters=query.filters,
                        limit=query.limit,
                        offset=query.offset,
                        boost_fields=query.boost_fields,
                        min_score=self.min_candidate_score
                    )
                    
                    results = await search_function(multi_model_query)
                    
                    # 应用模型权重
                    weight = self.model_weights.get(search_type.value, 0.8)
                    for result in results:
                        result.score *= weight
                        result.metadata['multi_model_type'] = search_type.value
                    
                    multi_model_results.extend(results)
            
            return multi_model_results
            
        except Exception as e:
            logger.error(f"Multi-model recall failed: {e}")
            return []
    
    def _expand_candidate_set(self, results: List[SearchResult], query: SearchQuery) -> List[SearchResult]:
        """扩展候选集"""
        try:
            # 按分数排序
            sorted_results = sorted(results, key=lambda x: x.score, reverse=True)
            
            # 扩展候选集
            target_size = min(len(sorted_results), query.limit * self.candidate_expansion_factor)
            expanded_candidates = sorted_results[:target_size]
            
            # 过滤低分结果
            filtered_candidates = [
                result for result in expanded_candidates 
                if result.score >= self.min_candidate_score
            ]
            
            return filtered_candidates
            
        except Exception as e:
            logger.error(f"Candidate set expansion failed: {e}")
            return results
    
    async def _negative_sampling_filter(self, results: List[SearchResult], query: SearchQuery) -> List[SearchResult]:
        """负采样过滤"""
        try:
            # 简单的负采样：移除明显不相关的结果
            filtered_results = []
            
            for result in results:
                # 基于内容长度、分数等简单过滤
                if (result.score > self.min_candidate_score and 
                    len(result.content.strip()) > 20 and
                    not self._is_likely_irrelevant(result, query)):
                    filtered_results.append(result)
            
            return filtered_results
            
        except Exception as e:
            logger.error(f"Negative sampling filter failed: {e}")
            return results
    
    def _is_likely_irrelevant(self, result: SearchResult, query: SearchQuery) -> bool:
        """判断结果是否可能不相关"""
        try:
            # 简单的不相关性检测
            content_lower = result.content.lower()
            query_lower = query.query.lower()
            
            # 检查是否包含查询关键词
            query_words = query_lower.split()
            content_words = content_lower.split()
            
            common_words = set(query_words) & set(content_words)
            
            # 如果没有共同词汇，可能不相关
            if len(common_words) == 0:
                return True
            
            # 如果内容太短，可能不相关
            if len(content_words) < 5:
                return True
            
            return False
            
        except Exception:
            return False
    
    def _deduplicate_results(self, results: List[SearchResult]) -> List[SearchResult]:
        """去重结果"""
        seen = set()
        deduplicated = []
        
        for result in results:
            # 使用内容哈希去重
            content_hash = hash(result.content[:200])  # 使用前200字符
            key = f"{result.id}_{result.source}_{content_hash}"
            
            if key not in seen:
                seen.add(key)
                deduplicated.append(result)
        
        return deduplicated
    
    def _evaluate_recall(self, results: List[SearchResult], query: SearchQuery) -> RecallMetrics:
        """评估召回率"""
        try:
            # 简化的召回率评估
            # 在实际应用中，需要真实的相关性标注
            
            total_retrieved = len(results)
            
            # 假设高分结果是相关的
            high_score_threshold = 0.5
            retrieved_relevant = len([r for r in results if r.score >= high_score_threshold])
            
            # 估算总相关文档数（这里使用简化假设）
            total_relevant = max(retrieved_relevant, query.limit)
            
            recall = retrieved_relevant / total_relevant if total_relevant > 0 else 0
            precision = retrieved_relevant / total_retrieved if total_retrieved > 0 else 0
            f1_score = 2 * (precision * recall) / (precision + recall) if (precision + recall) > 0 else 0
            
            return RecallMetrics(
                total_relevant=total_relevant,
                retrieved_relevant=retrieved_relevant,
                total_retrieved=total_retrieved,
                recall=recall,
                precision=precision,
                f1_score=f1_score
            )
            
        except Exception as e:
            logger.error(f"Recall evaluation failed: {e}")
            return RecallMetrics(0, 0, len(results), 0.0, 0.0, 0.0)
