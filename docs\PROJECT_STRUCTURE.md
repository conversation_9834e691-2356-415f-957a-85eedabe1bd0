# 项目结构说明

## 目录结构

```
search_chain/
├── README.md                           # 项目主文档
├── requirements.txt                    # 生产依赖
├── requirements-dev.txt               # 开发依赖
├── .env.example                       # 环境变量示例
├── .gitignore                         # Git忽略文件
│
├── config/                            # 配置文件目录
│   └── search_config.yaml            # 主配置文件
│
├── src/                               # 源代码目录
│   ├── __init__.py
│   │
│   ├── core/                          # 核心模块
│   │   ├── __init__.py
│   │   ├── interfaces.py              # 接口定义
│   │   └── search_manager.py          # 基础搜索管理器
│   │
│   ├── llm/                           # LLM增强模块
│   │   ├── __init__.py
│   │   ├── enhanced_search_manager.py # 增强搜索管理器
│   │   ├── document_processor.py      # 文档处理器
│   │   ├── query_processor.py         # 查询处理器
│   │   ├── result_optimizer.py        # 结果优化器
│   │   ├── advanced_vector_store.py   # 高级向量存储
│   │   └── feedback_system.py         # 反馈系统
│   │
│   ├── datasources/                   # 数据源模块
│   │   ├── __init__.py
│   │   ├── elasticsearch_source.py    # Elasticsearch数据源
│   │   ├── vector_source.py           # 向量数据源
│   │   └── database_source.py         # 数据库数据源
│   │
│   ├── strategies/                    # 搜索策略模块
│   │   ├── __init__.py
│   │   ├── keyword_strategy.py        # 关键词策略
│   │   ├── semantic_strategy.py       # 语义策略
│   │   └── hybrid_strategy.py         # 混合策略
│   │
│   ├── filters/                       # 筛选器模块
│   │   ├── __init__.py
│   │   └── filter_processor.py        # 筛选处理器
│   │
│   └── config/                        # 配置管理模块
│       ├── __init__.py
│       └── config_manager.py          # 配置管理器
│
├── examples/                          # 示例代码
│   ├── basic_usage.py                 # 基础使用示例
│   ├── llm_enhanced_usage.py          # LLM增强使用示例
│   └── complete_llm_demo.py           # 完整演示
│
├── tests/                             # 测试代码
│   ├── __init__.py
│   ├── test_config.py                 # 配置测试
│   ├── test_search_manager.py         # 搜索管理器测试
│   ├── test_llm_components.py         # LLM组件测试
│   └── test_integration.py            # 集成测试
│
├── docs/                              # 文档目录
│   ├── ARCHITECTURE_OVERVIEW.md       # 架构概览
│   ├── API_REFERENCE.md               # API参考
│   ├── TUTORIAL.md                    # 使用教程
│   ├── DEPLOYMENT_GUIDE.md            # 部署指南
│   ├── PERFORMANCE_OPTIMIZATION.md    # 性能优化
│   ├── LLM_OPTIMIZATION_GUIDE.md      # LLM优化指南
│   └── PROJECT_STRUCTURE.md           # 项目结构说明
│
├── scripts/                           # 脚本目录
│   ├── init_database.py               # 数据库初始化
│   ├── init_elasticsearch.py          # Elasticsearch初始化
│   └── performance_test.py            # 性能测试
│
├── data/                              # 数据目录
│   ├── documents/                     # 文档数据
│   ├── models/                        # 模型文件
│   └── feedback.db                    # 反馈数据库
│
├── logs/                              # 日志目录
│   └── search_system.log              # 系统日志
│
└── run_example.py                     # 运行示例脚本
```

## 核心模块说明

### 1. src/core/ - 核心模块

**interfaces.py**
- 定义系统的核心接口和数据结构
- 包含 `DataSource`、`SearchStrategy`、`SearchQuery`、`SearchResult` 等

**search_manager.py**
- 基础搜索管理器实现
- 协调各个数据源和搜索策略
- 提供统一的搜索接口

### 2. src/llm/ - LLM增强模块

**enhanced_search_manager.py**
- 继承基础搜索管理器
- 集成所有LLM增强功能
- 提供智能搜索的统一入口

**document_processor.py**
- 智能文档预处理
- 文档分块、摘要生成、关键词提取
- 支持批量处理和异步操作

**query_processor.py**
- 查询理解和扩展
- 意图识别、同义词生成
- 多语言查询支持

**result_optimizer.py**
- 搜索结果优化
- 相关性和质量评估
- 智能答案生成

**advanced_vector_store.py**
- 高级向量存储实现
- 支持FAISS、GPU加速
- 分层索引和智能聚类

**feedback_system.py**
- 用户反馈收集
- 系统学习和优化
- 性能监控和分析

### 3. src/datasources/ - 数据源模块

**elasticsearch_source.py**
- Elasticsearch数据源实现
- 支持全文搜索和向量搜索
- 提供健康检查和连接管理

**vector_source.py**
- 基础向量数据源
- 内存向量存储实现
- 支持相似度搜索

**database_source.py**
- 关系型数据库数据源
- PostgreSQL实现
- 支持SQL查询和全文搜索

### 4. src/strategies/ - 搜索策略模块

**keyword_strategy.py**
- 关键词搜索策略
- 基于BM25算法
- 支持多数据源结果合并

**semantic_strategy.py**
- 语义搜索策略
- 基于向量相似度
- 支持语义理解和匹配

**hybrid_strategy.py**
- 混合搜索策略
- 结合关键词和语义搜索
- 使用RRF算法融合结果

### 5. examples/ - 示例代码

**basic_usage.py**
- 基础功能演示
- 适合初学者了解系统

**llm_enhanced_usage.py**
- LLM增强功能演示
- 展示智能搜索能力

**complete_llm_demo.py**
- 完整功能演示
- 包含所有高级特性

### 6. tests/ - 测试代码

**test_config.py**
- 配置管理测试
- 验证配置加载和解析

**test_search_manager.py**
- 搜索管理器测试
- 核心搜索功能验证

**test_llm_components.py**
- LLM组件测试
- 各个增强模块的单元测试

**test_integration.py**
- 集成测试
- 端到端功能验证

## 配置文件说明

### config/search_config.yaml

主配置文件，包含：
- 数据源配置
- LLM模型配置
- 搜索策略配置
- 系统参数配置

### .env.example

环境变量模板，包含：
- API密钥配置
- 数据库连接信息
- 系统运行参数

## 数据目录说明

### data/documents/
存放文档数据文件，支持多种格式：
- JSON格式的结构化文档
- 文本文件
- CSV数据文件

### data/models/
存放模型文件：
- 预训练的嵌入模型
- 自定义模型权重
- 模型配置文件

### data/feedback.db
SQLite数据库文件，存储：
- 用户反馈数据
- 系统学习洞察
- 性能统计信息

## 日志系统

### logs/search_system.log
系统运行日志，记录：
- 搜索请求和响应
- 错误和异常信息
- 性能监控数据
- 系统状态变化

## 脚本工具

### scripts/init_database.py
数据库初始化脚本：
- 创建数据库表结构
- 初始化索引
- 导入初始数据

### scripts/init_elasticsearch.py
Elasticsearch初始化脚本：
- 创建索引映射
- 配置分析器
- 导入文档数据

### scripts/performance_test.py
性能测试脚本：
- 并发测试
- 响应时间测试
- 吞吐量测试
- 资源使用监控

## 开发工作流

### 1. 新功能开发
1. 在 `src/` 对应模块下创建新文件
2. 实现功能并添加文档字符串
3. 在 `tests/` 下添加对应测试
4. 在 `examples/` 下添加使用示例
5. 更新相关文档

### 2. 配置修改
1. 修改 `config/search_config.yaml`
2. 更新 `.env.example` 如需要
3. 更新文档中的配置说明
4. 测试配置变更的影响

### 3. 文档更新
1. 修改 `docs/` 下对应文档
2. 更新 `README.md` 如需要
3. 确保示例代码与文档一致
4. 检查链接和格式正确性

这个项目结构设计确保了代码的模块化、可维护性和可扩展性，同时提供了完整的文档和示例支持。
