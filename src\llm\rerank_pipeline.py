"""
重排序管道
支持多种reranker模型的组合和融合
"""
import logging
import asyncio
import numpy as np
from typing import List, Dict, Any, Optional, Tuple
from dataclasses import dataclass
from enum import Enum

from ..core.interfaces import SearchResult
from .reranker_models import (
    BaseReranker, BGEReranker, CrossEncoderReranker, 
    ColBERTReranker, CohereReranker, RerankResult
)

logger = logging.getLogger(__name__)


class FusionMethod(Enum):
    """融合方法"""
    WEIGHTED_AVERAGE = "weighted_average"
    RANK_FUSION = "rank_fusion"
    SCORE_FUSION = "score_fusion"
    ENSEMBLE_VOTING = "ensemble_voting"
    CASCADED = "cascaded"


@dataclass
class RerankConfig:
    """重排序配置"""
    rerankers: List[Dict[str, Any]]
    fusion_method: FusionMethod = FusionMethod.WEIGHTED_AVERAGE
    weights: List[float] = None
    top_k: int = 10
    enable_score_normalization: bool = True
    enable_diversity: bool = False
    diversity_lambda: float = 0.5


class RerankPipeline:
    """重排序管道"""
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        
        # 解析配置
        self.rerank_config = RerankConfig(
            rerankers=config.get('rerankers', []),
            fusion_method=FusionMethod(config.get('fusion_method', 'weighted_average')),
            weights=config.get('weights', []),
            top_k=config.get('top_k', 10),
            enable_score_normalization=config.get('enable_score_normalization', True),
            enable_diversity=config.get('enable_diversity', False),
            diversity_lambda=config.get('diversity_lambda', 0.5)
        )
        
        # 初始化reranker列表
        self.rerankers: List[BaseReranker] = []
        self.reranker_weights = self.rerank_config.weights or []
        
        # 初始化rerankers
        self._initialize_rerankers()
        
        self.initialized = False
    
    def _initialize_rerankers(self):
        """初始化reranker模型"""
        for i, reranker_config in enumerate(self.rerank_config.rerankers):
            reranker_type = reranker_config.get('type', 'bge').lower()
            
            if reranker_type == 'bge':
                reranker = BGEReranker(reranker_config)
            elif reranker_type == 'cross_encoder':
                reranker = CrossEncoderReranker(reranker_config)
            elif reranker_type == 'colbert':
                reranker = ColBERTReranker(reranker_config)
            elif reranker_type == 'cohere':
                reranker = CohereReranker(reranker_config)
            else:
                logger.warning(f"Unknown reranker type: {reranker_type}")
                continue
            
            self.rerankers.append(reranker)
            
            # 设置默认权重
            if len(self.reranker_weights) <= i:
                self.reranker_weights.append(1.0 / len(self.rerank_config.rerankers))
    
    async def initialize(self):
        """初始化管道"""
        try:
            # 并行初始化所有rerankers
            init_tasks = [reranker.initialize() for reranker in self.rerankers]
            await asyncio.gather(*init_tasks, return_exceptions=True)
            
            # 检查初始化结果
            for i, reranker in enumerate(self.rerankers):
                if not reranker.initialized:
                    logger.warning(f"Reranker {i} failed to initialize")
            
            self.initialized = True
            logger.info(f"Rerank pipeline initialized with {len(self.rerankers)} rerankers")
            
        except Exception as e:
            logger.error(f"Failed to initialize rerank pipeline: {e}")
            raise
    
    async def rerank(self, query: str, results: List[SearchResult]) -> List[RerankResult]:
        """执行重排序"""
        if not self.initialized:
            await self.initialize()
        
        if not results:
            return []
        
        try:
            if len(self.rerankers) == 1:
                # 单个reranker
                return await self.rerankers[0].rerank(query, results)
            else:
                # 多个reranker融合
                return await self._multi_reranker_fusion(query, results)
                
        except Exception as e:
            logger.error(f"Reranking failed: {e}")
            # 返回原始排序
            return [
                RerankResult(
                    original_result=result,
                    rerank_score=result.score,
                    original_rank=i,
                    new_rank=i,
                    explanation="Reranking failed, using original score"
                ) for i, result in enumerate(results)
            ]
    
    async def _multi_reranker_fusion(self, query: str, results: List[SearchResult]) -> List[RerankResult]:
        """多reranker融合"""
        try:
            # 并行执行所有rerankers
            rerank_tasks = []
            for reranker in self.rerankers:
                if reranker.initialized:
                    task = reranker.rerank(query, results)
                    rerank_tasks.append(task)
            
            if not rerank_tasks:
                logger.warning("No initialized rerankers available")
                return [
                    RerankResult(
                        original_result=result,
                        rerank_score=result.score,
                        original_rank=i,
                        new_rank=i,
                        explanation="No rerankers available"
                    ) for i, result in enumerate(results)
                ]
            
            # 获取所有reranker结果
            all_rerank_results = await asyncio.gather(*rerank_tasks, return_exceptions=True)
            
            # 过滤异常结果
            valid_results = []
            valid_weights = []
            for i, rerank_result in enumerate(all_rerank_results):
                if isinstance(rerank_result, list) and rerank_result:
                    valid_results.append(rerank_result)
                    valid_weights.append(self.reranker_weights[i] if i < len(self.reranker_weights) else 1.0)
                else:
                    logger.error(f"Reranker {i} failed: {rerank_result}")
            
            if not valid_results:
                logger.error("All rerankers failed")
                return [
                    RerankResult(
                        original_result=result,
                        rerank_score=result.score,
                        original_rank=i,
                        new_rank=i,
                        explanation="All rerankers failed"
                    ) for i, result in enumerate(results)
                ]
            
            # 执行融合
            fused_results = self._fuse_rerank_results(valid_results, valid_weights)
            
            # 应用多样性
            if self.rerank_config.enable_diversity:
                fused_results = self._apply_diversity(fused_results)
            
            return fused_results[:self.rerank_config.top_k]
            
        except Exception as e:
            logger.error(f"Multi-reranker fusion failed: {e}")
            return [
                RerankResult(
                    original_result=result,
                    rerank_score=result.score,
                    original_rank=i,
                    new_rank=i,
                    explanation="Fusion failed, using original score"
                ) for i, result in enumerate(results)
            ]
    
    def _fuse_rerank_results(self, all_results: List[List[RerankResult]], 
                           weights: List[float]) -> List[RerankResult]:
        """融合多个reranker的结果"""
        if self.rerank_config.fusion_method == FusionMethod.WEIGHTED_AVERAGE:
            return self._weighted_average_fusion(all_results, weights)
        elif self.rerank_config.fusion_method == FusionMethod.RANK_FUSION:
            return self._rank_fusion(all_results, weights)
        elif self.rerank_config.fusion_method == FusionMethod.SCORE_FUSION:
            return self._score_fusion(all_results, weights)
        elif self.rerank_config.fusion_method == FusionMethod.ENSEMBLE_VOTING:
            return self._ensemble_voting(all_results, weights)
        elif self.rerank_config.fusion_method == FusionMethod.CASCADED:
            return self._cascaded_fusion(all_results, weights)
        else:
            return self._weighted_average_fusion(all_results, weights)
    
    def _weighted_average_fusion(self, all_results: List[List[RerankResult]], 
                                weights: List[float]) -> List[RerankResult]:
        """加权平均融合"""
        # 创建结果映射
        result_map = {}
        
        for reranker_results, weight in zip(all_results, weights):
            for rerank_result in reranker_results:
                key = f"{rerank_result.original_result.id}_{rerank_result.original_result.source}"
                
                if key not in result_map:
                    result_map[key] = {
                        'original_result': rerank_result.original_result,
                        'scores': [],
                        'weights': [],
                        'original_rank': rerank_result.original_rank
                    }
                
                # 归一化分数
                normalized_score = self._normalize_score(rerank_result.rerank_score)
                result_map[key]['scores'].append(normalized_score)
                result_map[key]['weights'].append(weight)
        
        # 计算加权平均分数
        fused_results = []
        for key, data in result_map.items():
            if data['scores']:
                weighted_score = np.average(data['scores'], weights=data['weights'])
                
                fused_result = RerankResult(
                    original_result=data['original_result'],
                    rerank_score=float(weighted_score),
                    original_rank=data['original_rank'],
                    new_rank=0,  # 将在排序后设置
                    confidence=np.std(data['scores']),  # 使用标准差作为置信度
                    explanation=f"Weighted average of {len(data['scores'])} rerankers"
                )
                fused_results.append(fused_result)
        
        # 排序并更新排名
        fused_results.sort(key=lambda x: x.rerank_score, reverse=True)
        for i, result in enumerate(fused_results):
            result.new_rank = i
        
        return fused_results
    
    def _rank_fusion(self, all_results: List[List[RerankResult]], 
                    weights: List[float]) -> List[RerankResult]:
        """排名融合（RRF）"""
        result_map = {}
        k = 60  # RRF参数
        
        for reranker_results, weight in zip(all_results, weights):
            for rank, rerank_result in enumerate(reranker_results):
                key = f"{rerank_result.original_result.id}_{rerank_result.original_result.source}"
                
                if key not in result_map:
                    result_map[key] = {
                        'original_result': rerank_result.original_result,
                        'rrf_score': 0.0,
                        'original_rank': rerank_result.original_rank
                    }
                
                # 计算RRF分数
                rrf_score = weight / (k + rank + 1)
                result_map[key]['rrf_score'] += rrf_score
        
        # 创建融合结果
        fused_results = []
        for key, data in result_map.items():
            fused_result = RerankResult(
                original_result=data['original_result'],
                rerank_score=data['rrf_score'],
                original_rank=data['original_rank'],
                new_rank=0,
                explanation=f"RRF fusion score: {data['rrf_score']:.4f}"
            )
            fused_results.append(fused_result)
        
        # 排序
        fused_results.sort(key=lambda x: x.rerank_score, reverse=True)
        for i, result in enumerate(fused_results):
            result.new_rank = i
        
        return fused_results
    
    def _score_fusion(self, all_results: List[List[RerankResult]], 
                     weights: List[float]) -> List[RerankResult]:
        """分数融合"""
        # 类似加权平均，但使用原始分数
        result_map = {}
        
        for reranker_results, weight in zip(all_results, weights):
            for rerank_result in reranker_results:
                key = f"{rerank_result.original_result.id}_{rerank_result.original_result.source}"
                
                if key not in result_map:
                    result_map[key] = {
                        'original_result': rerank_result.original_result,
                        'total_score': 0.0,
                        'total_weight': 0.0,
                        'original_rank': rerank_result.original_rank
                    }
                
                result_map[key]['total_score'] += rerank_result.rerank_score * weight
                result_map[key]['total_weight'] += weight
        
        # 创建融合结果
        fused_results = []
        for key, data in result_map.items():
            if data['total_weight'] > 0:
                final_score = data['total_score'] / data['total_weight']
                
                fused_result = RerankResult(
                    original_result=data['original_result'],
                    rerank_score=final_score,
                    original_rank=data['original_rank'],
                    new_rank=0,
                    explanation=f"Score fusion: {final_score:.4f}"
                )
                fused_results.append(fused_result)
        
        # 排序
        fused_results.sort(key=lambda x: x.rerank_score, reverse=True)
        for i, result in enumerate(fused_results):
            result.new_rank = i
        
        return fused_results
    
    def _ensemble_voting(self, all_results: List[List[RerankResult]], 
                        weights: List[float]) -> List[RerankResult]:
        """集成投票"""
        # 基于排名的投票机制
        result_map = {}
        
        for reranker_results, weight in zip(all_results, weights):
            top_k = min(len(reranker_results), self.rerank_config.top_k)
            
            for rank, rerank_result in enumerate(reranker_results[:top_k]):
                key = f"{rerank_result.original_result.id}_{rerank_result.original_result.source}"
                
                if key not in result_map:
                    result_map[key] = {
                        'original_result': rerank_result.original_result,
                        'votes': 0.0,
                        'original_rank': rerank_result.original_rank
                    }
                
                # 投票权重与排名成反比
                vote_weight = weight * (top_k - rank) / top_k
                result_map[key]['votes'] += vote_weight
        
        # 创建融合结果
        fused_results = []
        for key, data in result_map.items():
            fused_result = RerankResult(
                original_result=data['original_result'],
                rerank_score=data['votes'],
                original_rank=data['original_rank'],
                new_rank=0,
                explanation=f"Ensemble votes: {data['votes']:.4f}"
            )
            fused_results.append(fused_result)
        
        # 排序
        fused_results.sort(key=lambda x: x.rerank_score, reverse=True)
        for i, result in enumerate(fused_results):
            result.new_rank = i
        
        return fused_results
    
    def _cascaded_fusion(self, all_results: List[List[RerankResult]], 
                        weights: List[float]) -> List[RerankResult]:
        """级联融合"""
        # 第一个reranker的结果作为基础，后续reranker进行调整
        if not all_results:
            return []
        
        base_results = all_results[0]
        
        for i, (reranker_results, weight) in enumerate(zip(all_results[1:], weights[1:]), 1):
            # 创建调整映射
            adjustment_map = {}
            for rerank_result in reranker_results:
                key = f"{rerank_result.original_result.id}_{rerank_result.original_result.source}"
                adjustment_map[key] = rerank_result.rerank_score
            
            # 调整基础结果
            for base_result in base_results:
                key = f"{base_result.original_result.id}_{base_result.original_result.source}"
                if key in adjustment_map:
                    # 加权调整分数
                    adjustment = adjustment_map[key] * weight
                    base_result.rerank_score = (base_result.rerank_score + adjustment) / 2
                    base_result.explanation += f" + cascaded adjustment from reranker {i}"
        
        # 重新排序
        base_results.sort(key=lambda x: x.rerank_score, reverse=True)
        for i, result in enumerate(base_results):
            result.new_rank = i
        
        return base_results
    
    def _normalize_score(self, score: float) -> float:
        """归一化分数"""
        if not self.rerank_config.enable_score_normalization:
            return score
        
        # 简单的sigmoid归一化
        return 1.0 / (1.0 + np.exp(-score))
    
    def _apply_diversity(self, results: List[RerankResult]) -> List[RerankResult]:
        """应用多样性"""
        try:
            # 简单的多样性实现：基于内容相似度
            diverse_results = []
            lambda_param = self.rerank_config.diversity_lambda
            
            for candidate in results:
                if not diverse_results:
                    diverse_results.append(candidate)
                    continue
                
                # 计算与已选结果的最大相似度
                max_similarity = 0.0
                for selected in diverse_results:
                    similarity = self._compute_content_similarity(
                        candidate.original_result.content,
                        selected.original_result.content
                    )
                    max_similarity = max(max_similarity, similarity)
                
                # 调整分数以考虑多样性
                diversity_penalty = lambda_param * max_similarity
                adjusted_score = candidate.rerank_score * (1 - diversity_penalty)
                candidate.rerank_score = adjusted_score
                
                diverse_results.append(candidate)
            
            # 重新排序
            diverse_results.sort(key=lambda x: x.rerank_score, reverse=True)
            for i, result in enumerate(diverse_results):
                result.new_rank = i
            
            return diverse_results
            
        except Exception as e:
            logger.error(f"Diversity application failed: {e}")
            return results
    
    def _compute_content_similarity(self, content1: str, content2: str) -> float:
        """计算内容相似度"""
        try:
            # 简单的Jaccard相似度
            words1 = set(content1.lower().split())
            words2 = set(content2.lower().split())
            
            intersection = len(words1 & words2)
            union = len(words1 | words2)
            
            return intersection / union if union > 0 else 0.0
            
        except Exception:
            return 0.0
    
    def get_pipeline_info(self) -> Dict[str, Any]:
        """获取管道信息"""
        return {
            'num_rerankers': len(self.rerankers),
            'reranker_types': [reranker.get_model_info()['name'] for reranker in self.rerankers],
            'fusion_method': self.rerank_config.fusion_method.value,
            'weights': self.reranker_weights,
            'top_k': self.rerank_config.top_k,
            'enable_diversity': self.rerank_config.enable_diversity
        }
