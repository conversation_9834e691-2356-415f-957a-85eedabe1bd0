"""
多模型支持演示
展示如何在系统中使用不同的大模型
"""
import os
import asyncio
import logging
from typing import Dict, Any

# 设置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# 添加项目路径
import sys
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from src.llm.query_processor import LLMQueryProcessor
from src.llm.model_adapter import create_model_adapter, ModelProvider
from src.utils.model_switcher import ModelSwitcher, quick_switch_preset
from src.core.interfaces import SearchQuery, SearchType


async def test_model_adapter():
    """测试模型适配器"""
    print("\n=== 测试模型适配器 ===")
    
    # 测试OpenAI模型
    if os.getenv('OPENAI_API_KEY'):
        print("\n1. 测试OpenAI模型")
        openai_config = {
            'provider': 'openai',
            'model': 'gpt-3.5-turbo',
            'temperature': 0.1,
            'max_tokens': 100
        }
        
        try:
            adapter = create_model_adapter(openai_config)
            response = await adapter.ainvoke("请简单介绍一下人工智能")
            print(f"OpenAI响应: {response[:100]}...")
        except Exception as e:
            print(f"OpenAI测试失败: {e}")
    
    # 测试通义千问（兼容OpenAI接口）
    if os.getenv('DASHSCOPE_API_KEY'):
        print("\n2. 测试通义千问")
        qwen_config = {
            'provider': 'openai',
            'model': 'qwen-turbo',
            'temperature': 0.1,
            'max_tokens': 100,
            'api_key': os.getenv('DASHSCOPE_API_KEY'),
            'base_url': 'https://dashscope.aliyuncs.com/compatible-mode/v1'
        }
        
        try:
            adapter = create_model_adapter(qwen_config)
            response = await adapter.ainvoke("请简单介绍一下机器学习")
            print(f"通义千问响应: {response[:100]}...")
        except Exception as e:
            print(f"通义千问测试失败: {e}")
    
    # 测试本地Ollama模型（如果可用）
    try:
        print("\n3. 测试本地Ollama模型")
        ollama_config = {
            'provider': 'ollama',
            'model': 'llama2',
            'base_url': 'http://localhost:11434'
        }
        
        adapter = create_model_adapter(ollama_config)
        response = await adapter.ainvoke("What is artificial intelligence?")
        print(f"Ollama响应: {response[:100]}...")
    except Exception as e:
        print(f"Ollama测试失败: {e}")


async def test_query_processor_with_different_models():
    """测试查询处理器使用不同模型"""
    print("\n=== 测试查询处理器多模型支持 ===")
    
    test_query = "人工智能在医疗领域的应用"
    
    # 测试配置列表
    test_configs = []
    
    # OpenAI配置
    if os.getenv('OPENAI_API_KEY'):
        test_configs.append({
            'name': 'OpenAI GPT-3.5',
            'config': {
                'provider': 'openai',
                'model': 'gpt-3.5-turbo',
                'temperature': 0.1,
                'max_tokens': 500
            }
        })
    
    # 通义千问配置
    if os.getenv('DASHSCOPE_API_KEY'):
        test_configs.append({
            'name': '通义千问',
            'config': {
                'provider': 'openai',
                'model': 'qwen-turbo',
                'temperature': 0.1,
                'max_tokens': 500,
                'api_key': os.getenv('DASHSCOPE_API_KEY'),
                'base_url': 'https://dashscope.aliyuncs.com/compatible-mode/v1'
            }
        })
    
    # 智谱AI配置
    if os.getenv('ZHIPUAI_API_KEY'):
        test_configs.append({
            'name': '智谱AI GLM-4',
            'config': {
                'provider': 'openai',
                'model': 'glm-4',
                'temperature': 0.1,
                'max_tokens': 500,
                'api_key': os.getenv('ZHIPUAI_API_KEY'),
                'base_url': 'https://open.bigmodel.cn/api/paas/v4/'
            }
        })
    
    # 测试每个模型
    for test_config in test_configs:
        print(f"\n--- 测试 {test_config['name']} ---")
        try:
            processor = LLMQueryProcessor(test_config['config'])
            analysis = await processor.analyze_query(test_query)
            
            print(f"查询意图: {analysis.intent.value}")
            print(f"关键词: {analysis.keywords}")
            print(f"扩展查询: {analysis.expanded_queries[:2]}")  # 只显示前2个
            print(f"置信度: {analysis.confidence}")
            
        except Exception as e:
            print(f"测试失败: {e}")


def test_model_switcher():
    """测试模型切换器"""
    print("\n=== 测试模型切换器 ===")
    
    # 创建模型切换器
    switcher = ModelSwitcher("config/search_config.yaml")
    
    # 显示当前配置
    print("\n当前模型配置:")
    switcher.print_current_status()
    
    # 列出可用模型
    print("\n可用模型:")
    available_models = switcher.list_available_models()
    for provider, models in available_models.items():
        print(f"{provider}: {', '.join(models[:3])}...")  # 只显示前3个
    
    # 演示切换到不同模型
    print("\n演示模型切换:")
    
    # 切换到通义千问
    if os.getenv('DASHSCOPE_API_KEY'):
        print("切换到通义千问...")
        qwen_config = switcher.create_model_config(
            'qwen', 'qwen-turbo',
            api_key=os.getenv('DASHSCOPE_API_KEY')
        )
        switcher.switch_model('query_processor', qwen_config)
        print("✓ 查询处理器已切换到通义千问")
    
    # 切换到智谱AI
    if os.getenv('ZHIPUAI_API_KEY'):
        print("切换到智谱AI...")
        zhipu_config = switcher.create_model_config(
            'zhipu', 'glm-4',
            api_key=os.getenv('ZHIPUAI_API_KEY')
        )
        switcher.switch_model('result_optimizer', zhipu_config)
        print("✓ 结果优化器已切换到智谱AI")
    
    # 显示更新后的配置
    print("\n更新后的配置:")
    switcher.print_current_status()


def demonstrate_quick_presets():
    """演示快速预设功能"""
    print("\n=== 演示快速预设功能 ===")
    
    switcher = ModelSwitcher("config/search_config.yaml")
    
    # 可用的预设
    presets = [
        'openai_gpt35',
        'qwen_turbo',
        'zhipu_glm4',
        'local_llama'
    ]
    
    print("可用预设:")
    for preset in presets:
        print(f"- {preset}")
    
    # 演示切换预设（不实际保存）
    print(f"\n演示切换到 'qwen_turbo' 预设:")
    try:
        # 这里只是演示，不实际保存
        print("✓ 所有组件已切换到通义千问")
        print("  - 文档处理器: qwen-turbo")
        print("  - 查询处理器: qwen-turbo") 
        print("  - 结果优化器: qwen-turbo")
    except Exception as e:
        print(f"切换失败: {e}")


def show_environment_setup():
    """显示环境变量设置指南"""
    print("\n=== 环境变量设置指南 ===")
    
    env_vars = {
        'OpenAI': 'OPENAI_API_KEY',
        'Anthropic': 'ANTHROPIC_API_KEY',
        '通义千问': 'DASHSCOPE_API_KEY',
        '智谱AI': 'ZHIPUAI_API_KEY',
        '百度文心': 'QIANFAN_ACCESS_KEY',
        '月之暗面': 'MOONSHOT_API_KEY',
        'DeepSeek': 'DEEPSEEK_API_KEY'
    }
    
    print("支持的模型提供商及其环境变量:")
    for provider, env_var in env_vars.items():
        status = "✓ 已设置" if os.getenv(env_var) else "✗ 未设置"
        print(f"{provider:10} {env_var:20} {status}")
    
    print("\n设置示例:")
    print("export OPENAI_API_KEY='your-openai-api-key'")
    print("export DASHSCOPE_API_KEY='your-qwen-api-key'")
    print("export ZHIPUAI_API_KEY='your-zhipu-api-key'")


async def main():
    """主函数"""
    print("🤖 多模型支持演示")
    print("=" * 50)
    
    # 显示环境设置
    show_environment_setup()
    
    # 测试模型适配器
    await test_model_adapter()
    
    # 测试查询处理器
    await test_query_processor_with_different_models()
    
    # 测试模型切换器
    test_model_switcher()
    
    # 演示快速预设
    demonstrate_quick_presets()
    
    print("\n" + "=" * 50)
    print("演示完成！")
    print("\n使用建议:")
    print("1. 设置相应的API密钥环境变量")
    print("2. 根据任务需求选择合适的模型")
    print("3. 使用ModelSwitcher进行模型切换")
    print("4. 监控不同模型的性能和成本")


if __name__ == "__main__":
    asyncio.run(main())
