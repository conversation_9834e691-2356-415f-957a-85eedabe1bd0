"""
模型切换工具 - 便于在不同模型之间切换
"""
import os
import yaml
import logging
from typing import Dict, Any, Optional, List
from pathlib import Path

logger = logging.getLogger(__name__)


class ModelSwitcher:
    """模型切换器"""
    
    def __init__(self, config_path: str = "config/search_config.yaml"):
        self.config_path = config_path
        self.config = {}
        self.load_config()
    
    def load_config(self):
        """加载配置文件"""
        try:
            with open(self.config_path, 'r', encoding='utf-8') as f:
                self.config = yaml.safe_load(f)
        except Exception as e:
            logger.error(f"Failed to load config: {e}")
            self.config = {}
    
    def save_config(self):
        """保存配置文件"""
        try:
            with open(self.config_path, 'w', encoding='utf-8') as f:
                yaml.dump(self.config, f, default_flow_style=False, allow_unicode=True)
            logger.info(f"Configuration saved to {self.config_path}")
        except Exception as e:
            logger.error(f"Failed to save config: {e}")
    
    def switch_model(self, component: str, model_config: Dict[str, Any]):
        """
        切换指定组件的模型
        
        Args:
            component: 组件名称 (document_processor, query_processor, result_optimizer)
            model_config: 新的模型配置
        """
        if 'llm' not in self.config:
            self.config['llm'] = {}
        
        if component not in self.config['llm']:
            self.config['llm'][component] = {}
        
        # 更新模型配置
        self.config['llm'][component].update(model_config)
        
        logger.info(f"Switched {component} to {model_config.get('provider', 'unknown')} - {model_config.get('model', 'unknown')}")
    
    def switch_all_models(self, model_config: Dict[str, Any]):
        """切换所有组件到相同模型"""
        components = ['document_processor', 'query_processor', 'result_optimizer']
        
        for component in components:
            self.switch_model(component, model_config.copy())
        
        logger.info(f"Switched all components to {model_config.get('provider', 'unknown')} - {model_config.get('model', 'unknown')}")
    
    def get_current_models(self) -> Dict[str, Dict[str, Any]]:
        """获取当前所有组件的模型配置"""
        llm_config = self.config.get('llm', {})
        
        current_models = {}
        components = ['document_processor', 'query_processor', 'result_optimizer']
        
        for component in components:
            if component in llm_config:
                current_models[component] = {
                    'provider': llm_config[component].get('provider', 'openai'),
                    'model': llm_config[component].get('model', 'gpt-3.5-turbo'),
                    'temperature': llm_config[component].get('temperature', 0.1),
                    'max_tokens': llm_config[component].get('max_tokens', 1000)
                }
            else:
                current_models[component] = {
                    'provider': 'openai',
                    'model': 'gpt-3.5-turbo',
                    'temperature': 0.1,
                    'max_tokens': 1000
                }
        
        return current_models
    
    def list_available_models(self) -> Dict[str, List[str]]:
        """列出可用的模型"""
        return {
            'openai': [
                'gpt-3.5-turbo',
                'gpt-4',
                'gpt-4-turbo',
                'gpt-4o'
            ],
            'anthropic': [
                'claude-3-haiku-20240307',
                'claude-3-sonnet-20240229',
                'claude-3-opus-20240229'
            ],
            'qwen': [
                'qwen-turbo',
                'qwen-plus',
                'qwen-max'
            ],
            'zhipu': [
                'glm-3-turbo',
                'glm-4'
            ],
            'baidu': [
                'ernie-bot-turbo',
                'ernie-bot',
                'ernie-bot-4'
            ],
            'moonshot': [
                'moonshot-v1-8k',
                'moonshot-v1-32k',
                'moonshot-v1-128k'
            ],
            'deepseek': [
                'deepseek-chat',
                'deepseek-coder'
            ],
            'ollama': [
                'llama2',
                'llama3',
                'mistral',
                'codellama'
            ]
        }
    
    def create_model_config(self, provider: str, model: str, **kwargs) -> Dict[str, Any]:
        """创建模型配置"""
        config = {
            'provider': provider,
            'model': model,
            'temperature': kwargs.get('temperature', 0.1),
            'max_tokens': kwargs.get('max_tokens', 1000)
        }
        
        # 添加特定提供商的配置
        if provider == 'azure_openai':
            config.update({
                'deployment_name': kwargs.get('deployment_name', model),
                'api_version': kwargs.get('api_version', '2023-12-01-preview')
            })
        elif provider == 'ollama':
            config.update({
                'base_url': kwargs.get('base_url', 'http://localhost:11434')
            })
        elif provider in ['qwen', 'zhipu', 'baidu', 'moonshot', 'deepseek']:
            # 这些都是兼容OpenAI接口的模型
            config['provider'] = 'openai'
            base_urls = {
                'qwen': 'https://dashscope.aliyuncs.com/compatible-mode/v1',
                'zhipu': 'https://open.bigmodel.cn/api/paas/v4/',
                'baidu': 'https://aip.baidubce.com/rpc/2.0/ai_custom/v1/wenxinworkshop/chat/',
                'moonshot': 'https://api.moonshot.cn/v1',
                'deepseek': 'https://api.deepseek.com/v1'
            }
            config['base_url'] = kwargs.get('base_url', base_urls.get(provider))
        
        # 添加其他配置
        for key, value in kwargs.items():
            if key not in config:
                config[key] = value
        
        return config
    
    def quick_switch(self, preset: str):
        """快速切换到预设配置"""
        presets = {
            'openai_gpt35': {
                'provider': 'openai',
                'model': 'gpt-3.5-turbo'
            },
            'openai_gpt4': {
                'provider': 'openai',
                'model': 'gpt-4'
            },
            'claude_sonnet': {
                'provider': 'anthropic',
                'model': 'claude-3-sonnet-20240229'
            },
            'qwen_turbo': {
                'provider': 'openai',
                'model': 'qwen-turbo',
                'base_url': 'https://dashscope.aliyuncs.com/compatible-mode/v1'
            },
            'zhipu_glm4': {
                'provider': 'openai',
                'model': 'glm-4',
                'base_url': 'https://open.bigmodel.cn/api/paas/v4/'
            },
            'local_llama': {
                'provider': 'ollama',
                'model': 'llama2',
                'base_url': 'http://localhost:11434'
            }
        }
        
        if preset not in presets:
            raise ValueError(f"Unknown preset: {preset}. Available: {list(presets.keys())}")
        
        config = presets[preset]
        self.switch_all_models(config)
        logger.info(f"Switched to preset: {preset}")
    
    def validate_model_config(self, config: Dict[str, Any]) -> bool:
        """验证模型配置"""
        required_fields = ['provider', 'model']
        
        for field in required_fields:
            if field not in config:
                logger.error(f"Missing required field: {field}")
                return False
        
        # 检查环境变量
        provider = config['provider']
        env_vars = {
            'openai': 'OPENAI_API_KEY',
            'anthropic': 'ANTHROPIC_API_KEY',
            'azure_openai': 'AZURE_OPENAI_API_KEY'
        }
        
        if provider in env_vars:
            env_var = env_vars[provider]
            if not os.getenv(env_var) and 'api_key' not in config:
                logger.warning(f"Environment variable {env_var} not set and no api_key in config")
        
        return True
    
    def print_current_status(self):
        """打印当前模型状态"""
        current_models = self.get_current_models()
        
        print("\n=== 当前模型配置 ===")
        for component, config in current_models.items():
            print(f"{component}:")
            print(f"  Provider: {config['provider']}")
            print(f"  Model: {config['model']}")
            print(f"  Temperature: {config['temperature']}")
            print(f"  Max Tokens: {config['max_tokens']}")
            print()


# 便捷函数
def switch_to_model(provider: str, model: str, component: Optional[str] = None, 
                   config_path: str = "config/search_config.yaml", **kwargs):
    """便捷函数：切换到指定模型"""
    switcher = ModelSwitcher(config_path)
    model_config = switcher.create_model_config(provider, model, **kwargs)
    
    if component:
        switcher.switch_model(component, model_config)
    else:
        switcher.switch_all_models(model_config)
    
    switcher.save_config()
    return switcher


def quick_switch_preset(preset: str, config_path: str = "config/search_config.yaml"):
    """便捷函数：快速切换预设"""
    switcher = ModelSwitcher(config_path)
    switcher.quick_switch(preset)
    switcher.save_config()
    return switcher


if __name__ == "__main__":
    # 示例用法
    switcher = ModelSwitcher()
    
    # 显示当前状态
    switcher.print_current_status()
    
    # 切换到通义千问
    # switcher.quick_switch('qwen_turbo')
    # switcher.save_config()
    
    # 或者手动配置
    # qwen_config = switcher.create_model_config('qwen', 'qwen-turbo')
    # switcher.switch_all_models(qwen_config)
    # switcher.save_config()
