"""
大模型适配器 - 支持多种模型提供商
"""
import os
import logging
from typing import Dict, Any, Optional, Union
from abc import ABC, abstractmethod
from enum import Enum

from langchain_openai import ChatOpenAI
from langchain_community.chat_models import ChatAnthropic
from langchain_community.llms import Ollama
from langchain.schema import BaseMessage, HumanMessage, AIMessage

logger = logging.getLogger(__name__)


class ModelProvider(Enum):
    """模型提供商枚举"""
    OPENAI = "openai"
    ANTHROPIC = "anthropic"
    AZURE_OPENAI = "azure_openai"
    OLLAMA = "ollama"
    ZHIPU = "zhipu"
    QWEN = "qwen"
    BAIDU = "baidu"


class BaseModelAdapter(ABC):
    """模型适配器基类"""
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.model = None
        self._initialize_model()
    
    @abstractmethod
    def _initialize_model(self):
        """初始化模型"""
        pass
    
    @abstractmethod
    async def ainvoke(self, prompt: str) -> str:
        """异步调用模型"""
        pass
    
    @abstractmethod
    def invoke(self, prompt: str) -> str:
        """同步调用模型"""
        pass


class OpenAIAdapter(BaseModelAdapter):
    """OpenAI模型适配器"""
    
    def _initialize_model(self):
        """初始化OpenAI模型"""
        api_key = self.config.get('api_key') or os.getenv('OPENAI_API_KEY')
        base_url = self.config.get('base_url') or os.getenv('OPENAI_API_BASE')
        
        if not api_key:
            raise ValueError("OpenAI API key is required")
        
        self.model = ChatOpenAI(
            model=self.config.get('model', 'gpt-3.5-turbo'),
            temperature=self.config.get('temperature', 0.1),
            max_tokens=self.config.get('max_tokens', 1000),
            openai_api_key=api_key,
            openai_api_base=base_url
        )
    
    async def ainvoke(self, prompt: str) -> str:
        """异步调用OpenAI模型"""
        try:
            response = await self.model.ainvoke([HumanMessage(content=prompt)])
            return response.content
        except Exception as e:
            logger.error(f"OpenAI model invocation failed: {e}")
            raise
    
    def invoke(self, prompt: str) -> str:
        """同步调用OpenAI模型"""
        try:
            response = self.model.invoke([HumanMessage(content=prompt)])
            return response.content
        except Exception as e:
            logger.error(f"OpenAI model invocation failed: {e}")
            raise


class AnthropicAdapter(BaseModelAdapter):
    """Anthropic模型适配器"""
    
    def _initialize_model(self):
        """初始化Anthropic模型"""
        api_key = self.config.get('api_key') or os.getenv('ANTHROPIC_API_KEY')
        
        if not api_key:
            raise ValueError("Anthropic API key is required")
        
        self.model = ChatAnthropic(
            model=self.config.get('model', 'claude-3-sonnet-20240229'),
            temperature=self.config.get('temperature', 0.1),
            max_tokens=self.config.get('max_tokens', 1000),
            anthropic_api_key=api_key
        )
    
    async def ainvoke(self, prompt: str) -> str:
        """异步调用Anthropic模型"""
        try:
            response = await self.model.ainvoke([HumanMessage(content=prompt)])
            return response.content
        except Exception as e:
            logger.error(f"Anthropic model invocation failed: {e}")
            raise
    
    def invoke(self, prompt: str) -> str:
        """同步调用Anthropic模型"""
        try:
            response = self.model.invoke([HumanMessage(content=prompt)])
            return response.content
        except Exception as e:
            logger.error(f"Anthropic model invocation failed: {e}")
            raise


class AzureOpenAIAdapter(BaseModelAdapter):
    """Azure OpenAI模型适配器"""
    
    def _initialize_model(self):
        """初始化Azure OpenAI模型"""
        api_key = self.config.get('api_key') or os.getenv('AZURE_OPENAI_API_KEY')
        endpoint = self.config.get('endpoint') or os.getenv('AZURE_OPENAI_ENDPOINT')
        api_version = self.config.get('api_version', '2023-12-01-preview')
        
        if not api_key or not endpoint:
            raise ValueError("Azure OpenAI API key and endpoint are required")
        
        self.model = ChatOpenAI(
            model=self.config.get('model', 'gpt-35-turbo'),
            temperature=self.config.get('temperature', 0.1),
            max_tokens=self.config.get('max_tokens', 1000),
            openai_api_key=api_key,
            openai_api_base=f"{endpoint}/openai/deployments/{self.config.get('deployment_name', self.config.get('model', 'gpt-35-turbo'))}/chat/completions?api-version={api_version}",
            openai_api_type="azure"
        )
    
    async def ainvoke(self, prompt: str) -> str:
        """异步调用Azure OpenAI模型"""
        try:
            response = await self.model.ainvoke([HumanMessage(content=prompt)])
            return response.content
        except Exception as e:
            logger.error(f"Azure OpenAI model invocation failed: {e}")
            raise
    
    def invoke(self, prompt: str) -> str:
        """同步调用Azure OpenAI模型"""
        try:
            response = self.model.invoke([HumanMessage(content=prompt)])
            return response.content
        except Exception as e:
            logger.error(f"Azure OpenAI model invocation failed: {e}")
            raise


class OllamaAdapter(BaseModelAdapter):
    """Ollama本地模型适配器"""
    
    def _initialize_model(self):
        """初始化Ollama模型"""
        base_url = self.config.get('base_url', 'http://localhost:11434')
        
        self.model = Ollama(
            model=self.config.get('model', 'llama2'),
            base_url=base_url,
            temperature=self.config.get('temperature', 0.1)
        )
    
    async def ainvoke(self, prompt: str) -> str:
        """异步调用Ollama模型"""
        try:
            # Ollama的异步调用
            response = await self.model.ainvoke(prompt)
            return response
        except Exception as e:
            logger.error(f"Ollama model invocation failed: {e}")
            raise
    
    def invoke(self, prompt: str) -> str:
        """同步调用Ollama模型"""
        try:
            response = self.model.invoke(prompt)
            return response
        except Exception as e:
            logger.error(f"Ollama model invocation failed: {e}")
            raise


class ModelAdapterFactory:
    """模型适配器工厂"""
    
    _adapters = {
        ModelProvider.OPENAI: OpenAIAdapter,
        ModelProvider.ANTHROPIC: AnthropicAdapter,
        ModelProvider.AZURE_OPENAI: AzureOpenAIAdapter,
        ModelProvider.OLLAMA: OllamaAdapter,
    }
    
    @classmethod
    def create_adapter(cls, provider: Union[str, ModelProvider], config: Dict[str, Any]) -> BaseModelAdapter:
        """创建模型适配器"""
        if isinstance(provider, str):
            try:
                provider = ModelProvider(provider.lower())
            except ValueError:
                raise ValueError(f"Unsupported model provider: {provider}")
        
        adapter_class = cls._adapters.get(provider)
        if not adapter_class:
            raise ValueError(f"No adapter found for provider: {provider}")
        
        return adapter_class(config)
    
    @classmethod
    def register_adapter(cls, provider: ModelProvider, adapter_class: type):
        """注册新的模型适配器"""
        cls._adapters[provider] = adapter_class
    
    @classmethod
    def get_supported_providers(cls) -> list:
        """获取支持的模型提供商列表"""
        return list(cls._adapters.keys())


def create_model_adapter(config: Dict[str, Any]) -> BaseModelAdapter:
    """便捷函数：创建模型适配器"""
    provider = config.get('provider', 'openai')
    return ModelAdapterFactory.create_adapter(provider, config)
