"""
关系数据库数据源实现
"""
import logging
import asyncio
from typing import List, Dict, Any, Optional
import asyncpg
from contextlib import asynccontextmanager

from ..core.interfaces import DataSource, SearchQuery, SearchResult, SearchFilter, SearchType

logger = logging.getLogger(__name__)


class PostgreSQLSource(DataSource):
    """PostgreSQL数据源"""
    
    def __init__(self, config: Dict[str, Any]):
        super().__init__(config)
        self.pool = None
        self.host = config.get('host', 'localhost')
        self.port = config.get('port', 5432)
        self.database = config.get('database', 'search_db')
        self.username = config.get('username', 'postgres')
        self.password = config.get('password', '')
        self.table_name = config.get('table_name', 'documents')
        self.content_field = config.get('content_field', 'content')
        self.id_field = config.get('id_field', 'id')
        
    async def connect(self) -> bool:
        """连接PostgreSQL"""
        try:
            dsn = f"postgresql://{self.username}:{self.password}@{self.host}:{self.port}/{self.database}"
            self.pool = await asyncpg.create_pool(dsn, min_size=1, max_size=10)
            
            # 测试连接
            async with self.pool.acquire() as conn:
                await conn.fetchval("SELECT 1")
            
            logger.info(f"Connected to PostgreSQL at {self.host}:{self.port}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to connect to PostgreSQL: {e}")
            return False
    
    async def disconnect(self) -> None:
        """断开连接"""
        if self.pool:
            await self.pool.close()
            logger.info("Disconnected from PostgreSQL")
    
    async def search(self, query: SearchQuery) -> List[SearchResult]:
        """执行数据库检索"""
        if not self.pool:
            raise ConnectionError("Not connected to PostgreSQL")
        
        try:
            sql_query, params = self._build_sql_query(query)
            
            async with self.pool.acquire() as conn:
                rows = await conn.fetch(sql_query, *params)
            
            results = []
            for row in rows:
                # 计算简单的文本匹配分数
                score = self._calculate_score(query.query, row[self.content_field])
                
                if score < query.min_score:
                    continue
                
                result = SearchResult(
                    id=str(row[self.id_field]),
                    content=row[self.content_field],
                    score=score,
                    metadata=dict(row),
                    source=self.name,
                    search_type=query.search_type
                )
                results.append(result)
            
            # 按分数排序
            results.sort(key=lambda x: x.score, reverse=True)
            return results[:query.limit]
            
        except Exception as e:
            logger.error(f"Database search failed: {e}")
            return []
    
    def _build_sql_query(self, query: SearchQuery) -> tuple:
        """构建SQL查询"""
        base_sql = f"SELECT * FROM {self.table_name}"
        conditions = []
        params = []
        
        # 构建文本搜索条件
        if query.query.strip():
            if query.search_type == SearchType.KEYWORD:
                conditions.append(f"{self.content_field} ILIKE $1")
                params.append(f"%{query.query}%")
            else:
                # 使用PostgreSQL的全文搜索
                conditions.append(f"to_tsvector({self.content_field}) @@ plainto_tsquery($1)")
                params.append(query.query)
        
        # 应用筛选条件
        if query.filters:
            for filter_condition in query.filters:
                condition, param = self._build_filter_condition(filter_condition, len(params) + 1)
                if condition:
                    conditions.append(condition)
                    if param is not None:
                        params.append(param)
        
        # 组装完整查询
        if conditions:
            base_sql += " WHERE " + " AND ".join(conditions)
        
        base_sql += f" LIMIT {query.limit} OFFSET {query.offset}"
        
        return base_sql, params
    
    def _build_filter_condition(self, filter_condition: SearchFilter, param_index: int) -> tuple:
        """构建筛选条件"""
        field = filter_condition.field
        operator = filter_condition.operator
        value = filter_condition.value
        
        if operator == "eq":
            return f"{field} = ${param_index}", value
        elif operator == "ne":
            return f"{field} != ${param_index}", value
        elif operator == "gt":
            return f"{field} > ${param_index}", value
        elif operator == "lt":
            return f"{field} < ${param_index}", value
        elif operator == "gte":
            return f"{field} >= ${param_index}", value
        elif operator == "lte":
            return f"{field} <= ${param_index}", value
        elif operator == "in":
            placeholders = ",".join([f"${param_index + i}" for i in range(len(value))])
            return f"{field} IN ({placeholders})", None  # 需要特殊处理
        elif operator == "contains":
            return f"{field} ILIKE ${param_index}", f"%{value}%"
        
        return None, None
    
    def _calculate_score(self, query: str, content: str) -> float:
        """计算简单的文本匹配分数"""
        if not query or not content:
            return 0.0
        
        query_lower = query.lower()
        content_lower = content.lower()
        
        # 简单的关键词匹配计分
        query_words = query_lower.split()
        content_words = content_lower.split()
        
        matches = sum(1 for word in query_words if word in content_words)
        score = matches / len(query_words) if query_words else 0.0
        
        # 考虑完整匹配
        if query_lower in content_lower:
            score += 0.5
        
        return min(score, 1.0)
    
    async def health_check(self) -> bool:
        """健康检查"""
        try:
            if not self.pool:
                return False
            
            async with self.pool.acquire() as conn:
                await conn.fetchval("SELECT 1")
                
                # 检查表是否存在
                exists = await conn.fetchval(
                    "SELECT EXISTS (SELECT FROM information_schema.tables WHERE table_name = $1)",
                    self.table_name
                )
                return exists
                
        except Exception as e:
            logger.error(f"Health check failed: {e}")
            return False
