"""
配置管理器测试
"""
import pytest
import tempfile
import os
from pathlib import Path
import sys

# 添加项目根目录到路径
sys.path.append(str(Path(__file__).parent.parent))

from src.config.config_manager import Config<PERSON>ana<PERSON>, DataSourceConfig, StrategyConfig


class TestConfigManager:
    """配置管理器测试类"""
    
    @pytest.fixture
    def temp_config_file(self):
        """临时配置文件fixture"""
        config_content = """
data_sources:
  - name: "test_es"
    type: "elasticsearch"
    host: "localhost"
    port: 9200
    enabled: true

strategies:
  keyword:
    enabled: true
    config:
      boost_weights: {}

search:
  default_limit: 20
  max_limit: 200
"""
        
        with tempfile.NamedTemporaryFile(mode='w', suffix='.yaml', delete=False) as f:
            f.write(config_content)
            temp_file = f.name
        
        yield temp_file
        
        # 清理
        os.unlink(temp_file)
    
    def test_load_default_config(self):
        """测试加载默认配置"""
        config_manager = ConfigManager()
        config_manager._load_default_config()
        
        assert len(config_manager.data_sources) >= 1
        assert len(config_manager.strategies) >= 1
        assert config_manager.search_config.default_limit == 10
    
    def test_load_config_from_file(self, temp_config_file):
        """测试从文件加载配置"""
        config_manager = ConfigManager(temp_config_file)
        result = config_manager.load_config()
        
        assert result is True
        assert "test_es" in config_manager.data_sources
        assert config_manager.data_sources["test_es"].type == "elasticsearch"
        assert config_manager.search_config.default_limit == 20
    
    def test_load_nonexistent_config(self):
        """测试加载不存在的配置文件"""
        config_manager = ConfigManager("nonexistent.yaml")
        result = config_manager.load_config()
        
        # 应该加载默认配置
        assert result is True
        assert len(config_manager.data_sources) >= 1
    
    def test_add_data_source(self):
        """测试添加数据源配置"""
        config_manager = ConfigManager()
        config_manager._load_default_config()
        
        new_ds_config = {
            'name': 'new_test_source',
            'type': 'vector_db',
            'model_name': 'test-model',
            'enabled': True
        }
        
        result = config_manager.add_data_source(new_ds_config)
        
        assert result is True
        assert 'new_test_source' in config_manager.data_sources
        assert config_manager.data_sources['new_test_source'].type == 'vector_db'
    
    def test_add_invalid_data_source(self):
        """测试添加无效数据源配置"""
        config_manager = ConfigManager()
        config_manager._load_default_config()
        
        invalid_config = {
            'type': 'invalid_type'  # 缺少必需的name字段
        }
        
        result = config_manager.add_data_source(invalid_config)
        assert result is False
    
    def test_remove_data_source(self):
        """测试移除数据源配置"""
        config_manager = ConfigManager()
        config_manager._load_default_config()
        
        # 先添加一个数据源
        config_manager.add_data_source({
            'name': 'to_remove',
            'type': 'elasticsearch',
            'enabled': True
        })
        
        # 然后移除它
        result = config_manager.remove_data_source('to_remove')
        
        assert result is True
        assert 'to_remove' not in config_manager.data_sources
    
    def test_update_data_source(self):
        """测试更新数据源配置"""
        config_manager = ConfigManager()
        config_manager._load_default_config()
        
        # 添加一个数据源
        config_manager.add_data_source({
            'name': 'to_update',
            'type': 'elasticsearch',
            'host': 'localhost',
            'enabled': True
        })
        
        # 更新配置
        update_config = {
            'host': 'remote-host',
            'port': 9200
        }
        
        result = config_manager.update_data_source('to_update', update_config)
        
        assert result is True
        assert config_manager.data_sources['to_update'].host == 'remote-host'
        assert config_manager.data_sources['to_update'].port == 9200
    
    def test_get_enabled_data_sources(self):
        """测试获取启用的数据源"""
        config_manager = ConfigManager()
        config_manager._load_default_config()
        
        # 添加启用和禁用的数据源
        config_manager.add_data_source({
            'name': 'enabled_source',
            'type': 'elasticsearch',
            'enabled': True
        })
        
        config_manager.add_data_source({
            'name': 'disabled_source',
            'type': 'elasticsearch',
            'enabled': False
        })
        
        enabled_sources = config_manager.get_enabled_data_sources()
        
        assert 'enabled_source' in enabled_sources
        assert 'disabled_source' not in enabled_sources
    
    def test_validate_config(self):
        """测试配置验证"""
        config_manager = ConfigManager()
        config_manager._load_default_config()
        
        validation_result = config_manager.validate_config()
        
        assert 'valid' in validation_result
        assert 'errors' in validation_result
        assert 'warnings' in validation_result
    
    def test_save_config(self):
        """测试保存配置"""
        config_manager = ConfigManager()
        config_manager._load_default_config()
        
        with tempfile.NamedTemporaryFile(mode='w', suffix='.yaml', delete=False) as f:
            temp_file = f.name
        
        try:
            result = config_manager.save_config(temp_file)
            assert result is True
            assert os.path.exists(temp_file)
            
            # 验证保存的配置可以重新加载
            new_config_manager = ConfigManager(temp_file)
            load_result = new_config_manager.load_config()
            assert load_result is True
            
        finally:
            if os.path.exists(temp_file):
                os.unlink(temp_file)


if __name__ == "__main__":
    pytest.main([__file__])
