#!/usr/bin/env python3
"""
混合检索系统运行脚本
"""
import asyncio
import argparse
import logging
import sys
from pathlib import Path

# 添加src目录到Python路径
sys.path.insert(0, str(Path(__file__).parent / "src"))

from examples.basic_usage import main as run_examples


def setup_logging(level=logging.INFO):
    """设置日志"""
    logging.basicConfig(
        level=level,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.StreamHandler(sys.stdout),
            logging.FileHandler('logs/search.log', mode='a', encoding='utf-8')
        ]
    )
    
    # 创建日志目录
    Path('logs').mkdir(exist_ok=True)


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='混合检索系统')
    parser.add_argument(
        '--example', 
        action='store_true', 
        help='运行示例程序'
    )
    parser.add_argument(
        '--config', 
        default='config/search_config.yaml',
        help='配置文件路径'
    )
    parser.add_argument(
        '--log-level',
        choices=['DEBUG', 'INFO', 'WARNING', 'ERROR'],
        default='INFO',
        help='日志级别'
    )
    
    args = parser.parse_args()
    
    # 设置日志
    log_level = getattr(logging, args.log_level)
    setup_logging(log_level)
    
    logger = logging.getLogger(__name__)
    logger.info("启动混合检索系统")
    
    try:
        if args.example:
            logger.info("运行示例程序")
            asyncio.run(run_examples())
        else:
            print("混合检索系统")
            print("使用 --example 运行示例程序")
            print("使用 --help 查看更多选项")
            
    except KeyboardInterrupt:
        logger.info("程序被用户中断")
    except Exception as e:
        logger.error(f"程序执行失败: {e}", exc_info=True)
        sys.exit(1)
    
    logger.info("程序执行完成")


if __name__ == "__main__":
    main()
