"""
向量数据库数据源实现（示例使用内存向量存储）
"""
import logging
import numpy as np
from typing import List, Dict, Any, Optional
from sentence_transformers import SentenceTransformer
from sklearn.metrics.pairwise import cosine_similarity

from ..core.interfaces import DataSource, SearchQuery, SearchResult, SearchFilter, SearchType

logger = logging.getLogger(__name__)


class VectorSource(DataSource):
    """向量数据库数据源（内存实现示例）"""
    
    def __init__(self, config: Dict[str, Any]):
        super().__init__(config)
        self.model_name = config.get('model_name', 'all-MiniLM-L6-v2')
        self.model = None
        self.documents = []  # 存储文档
        self.vectors = None  # 存储向量
        self.connected = False
        
    async def connect(self) -> bool:
        """连接向量数据库"""
        try:
            # 加载句子转换模型
            self.model = SentenceTransformer(self.model_name)
            self.connected = True
            logger.info(f"Vector source connected with model: {self.model_name}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to connect to vector source: {e}")
            return False
    
    async def disconnect(self) -> None:
        """断开连接"""
        self.connected = False
        logger.info("Disconnected from vector source")
    
    async def search(self, query: SearchQuery) -> List[SearchResult]:
        """执行向量检索"""
        if not self.connected or not self.model:
            raise ConnectionError("Not connected to vector source")
        
        if not self.documents:
            return []
        
        try:
            # 将查询转换为向量
            query_vector = self.model.encode([query.query])
            
            # 计算相似度
            similarities = cosine_similarity(query_vector, self.vectors)[0]
            
            # 获取top-k结果
            top_indices = np.argsort(similarities)[::-1][:query.limit]
            
            results = []
            for idx in top_indices:
                if similarities[idx] < query.min_score:
                    continue
                
                doc = self.documents[idx]
                
                # 应用筛选条件
                if query.filters and not self._apply_filters(doc, query.filters):
                    continue
                
                result = SearchResult(
                    id=doc.get('id', str(idx)),
                    content=doc.get('content', ''),
                    score=float(similarities[idx]),
                    metadata=doc,
                    source=self.name,
                    search_type=SearchType.SEMANTIC
                )
                results.append(result)
            
            return results[:query.limit]
            
        except Exception as e:
            logger.error(f"Vector search failed: {e}")
            return []
    
    def _apply_filters(self, document: Dict[str, Any], filters: List[SearchFilter]) -> bool:
        """应用筛选条件"""
        for filter_condition in filters:
            field = filter_condition.field
            operator = filter_condition.operator
            value = filter_condition.value
            
            if field not in document:
                return False
            
            doc_value = document[field]
            
            if operator == "eq" and doc_value != value:
                return False
            elif operator == "ne" and doc_value == value:
                return False
            elif operator == "gt" and doc_value <= value:
                return False
            elif operator == "lt" and doc_value >= value:
                return False
            elif operator == "gte" and doc_value < value:
                return False
            elif operator == "lte" and doc_value > value:
                return False
            elif operator == "in" and doc_value not in value:
                return False
            elif operator == "not_in" and doc_value in value:
                return False
            elif operator == "contains" and str(value).lower() not in str(doc_value).lower():
                return False
        
        return True
    
    async def add_documents(self, documents: List[Dict[str, Any]]) -> bool:
        """添加文档到向量数据库"""
        if not self.connected or not self.model:
            return False
        
        try:
            # 提取文档内容
            contents = [doc.get('content', '') for doc in documents]
            
            # 生成向量
            vectors = self.model.encode(contents)
            
            # 存储文档和向量
            self.documents.extend(documents)
            
            if self.vectors is None:
                self.vectors = vectors
            else:
                self.vectors = np.vstack([self.vectors, vectors])
            
            logger.info(f"Added {len(documents)} documents to vector source")
            return True
            
        except Exception as e:
            logger.error(f"Failed to add documents: {e}")
            return False
    
    async def health_check(self) -> bool:
        """健康检查"""
        return self.connected and self.model is not None
