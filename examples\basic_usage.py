"""
混合检索系统基本使用示例
"""
import asyncio
import logging
from pathlib import Path
import sys

# 添加项目根目录到路径
sys.path.append(str(Path(__file__).parent.parent))

from src.core.search_manager import SearchManager
from src.core.interfaces import SearchQuery, SearchFilter, SearchType
from src.config.config_manager import ConfigManager

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

logger = logging.getLogger(__name__)


async def basic_search_example():
    """基本检索示例"""
    print("=== 基本检索示例 ===")
    
    # 加载配置
    config_manager = ConfigManager("config/search_config.yaml")
    config_manager.load_config()
    
    # 创建搜索管理器
    search_manager = SearchManager(config_manager.get_full_config())
    
    try:
        # 初始化搜索管理器
        await search_manager.initialize()
        
        # 添加一些示例数据到向量数据源
        vector_source = None
        for ds in search_manager.data_sources:
            if hasattr(ds, 'add_documents'):
                vector_source = ds
                break
        
        if vector_source:
            sample_docs = [
                {
                    'id': '1',
                    'content': 'Python是一种高级编程语言，广泛用于数据科学和机器学习',
                    'category': 'programming',
                    'tags': ['python', 'programming', 'data-science'],
                    'created_at': '2024-01-01T10:00:00'
                },
                {
                    'id': '2',
                    'content': '机器学习是人工智能的一个分支，使用算法从数据中学习模式',
                    'category': 'ai',
                    'tags': ['machine-learning', 'ai', 'algorithms'],
                    'created_at': '2024-01-02T10:00:00'
                },
                {
                    'id': '3',
                    'content': 'Elasticsearch是一个分布式搜索和分析引擎',
                    'category': 'database',
                    'tags': ['elasticsearch', 'search', 'database'],
                    'created_at': '2024-01-03T10:00:00'
                }
            ]
            
            await vector_source.add_documents(sample_docs)
            print(f"添加了 {len(sample_docs)} 个示例文档")
        
        # 1. 关键词检索
        print("\n1. 关键词检索:")
        keyword_query = SearchQuery(
            query="Python编程",
            search_type=SearchType.KEYWORD,
            limit=5
        )
        
        results = await search_manager.search(keyword_query)
        print(f"找到 {len(results)} 个结果:")
        for i, result in enumerate(results, 1):
            print(f"  {i}. [分数: {result.score:.3f}] {result.content[:100]}...")
        
        # 2. 语义检索
        print("\n2. 语义检索:")
        semantic_query = SearchQuery(
            query="人工智能算法",
            search_type=SearchType.SEMANTIC,
            limit=5
        )
        
        results = await search_manager.search(semantic_query)
        print(f"找到 {len(results)} 个结果:")
        for i, result in enumerate(results, 1):
            print(f"  {i}. [分数: {result.score:.3f}] {result.content[:100]}...")
        
        # 3. 混合检索
        print("\n3. 混合检索:")
        hybrid_query = SearchQuery(
            query="搜索引擎技术",
            search_type=SearchType.HYBRID,
            limit=5
        )
        
        results = await search_manager.search(hybrid_query)
        print(f"找到 {len(results)} 个结果:")
        for i, result in enumerate(results, 1):
            print(f"  {i}. [分数: {result.score:.3f}] {result.content[:100]}...")
        
    except Exception as e:
        logger.error(f"搜索示例失败: {e}")
    
    finally:
        await search_manager.cleanup()


async def filtered_search_example():
    """带筛选条件的检索示例"""
    print("\n=== 带筛选条件的检索示例 ===")
    
    config_manager = ConfigManager("config/search_config.yaml")
    config_manager.load_config()
    
    search_manager = SearchManager(config_manager.get_full_config())
    
    try:
        await search_manager.initialize()
        
        # 添加示例数据
        vector_source = None
        for ds in search_manager.data_sources:
            if hasattr(ds, 'add_documents'):
                vector_source = ds
                break
        
        if vector_source:
            sample_docs = [
                {
                    'id': '4',
                    'content': 'Java是一种面向对象的编程语言',
                    'category': 'programming',
                    'tags': ['java', 'oop'],
                    'created_at': '2024-01-04T10:00:00'
                },
                {
                    'id': '5',
                    'content': '深度学习是机器学习的一个子领域',
                    'category': 'ai',
                    'tags': ['deep-learning', 'neural-networks'],
                    'created_at': '2024-01-05T10:00:00'
                }
            ]
            
            await vector_source.add_documents(sample_docs)
        
        # 使用筛选条件的检索
        filters = [
            SearchFilter(field="category", operator="eq", value="programming"),
            SearchFilter(field="created_at", operator="gte", value="2024-01-01T00:00:00")
        ]
        
        filtered_query = SearchQuery(
            query="编程语言",
            search_type=SearchType.HYBRID,
            filters=filters,
            limit=10
        )
        
        results = await search_manager.search(filtered_query)
        print(f"筛选后找到 {len(results)} 个结果:")
        for i, result in enumerate(results, 1):
            category = result.metadata.get('category', 'unknown')
            print(f"  {i}. [分类: {category}] [分数: {result.score:.3f}] {result.content[:80]}...")
    
    except Exception as e:
        logger.error(f"筛选检索示例失败: {e}")
    
    finally:
        await search_manager.cleanup()


async def health_check_example():
    """健康检查示例"""
    print("\n=== 健康检查示例 ===")
    
    config_manager = ConfigManager("config/search_config.yaml")
    config_manager.load_config()
    
    search_manager = SearchManager(config_manager.get_full_config())
    
    try:
        await search_manager.initialize()
        
        # 执行健康检查
        health_status = await search_manager.health_check()
        
        print("系统健康状态:")
        print(f"  整体状态: {health_status['overall_status']}")
        print(f"  已初始化: {health_status['initialized']}")
        print(f"  可用策略: {health_status['strategies']}")
        
        print("  数据源状态:")
        for name, status in health_status['data_sources'].items():
            print(f"    {name}: {status['status']} ({status.get('type', 'unknown')})")
    
    except Exception as e:
        logger.error(f"健康检查示例失败: {e}")
    
    finally:
        await search_manager.cleanup()


async def main():
    """主函数"""
    print("混合检索系统示例")
    print("=" * 50)
    
    try:
        await basic_search_example()
        await filtered_search_example()
        await health_check_example()
        
    except KeyboardInterrupt:
        print("\n程序被用户中断")
    except Exception as e:
        logger.error(f"程序执行失败: {e}")
    
    print("\n示例执行完成")


if __name__ == "__main__":
    asyncio.run(main())
