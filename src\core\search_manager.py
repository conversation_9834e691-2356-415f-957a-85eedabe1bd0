"""
混合检索管理器实现
"""
import logging
import asyncio
from typing import List, Dict, Any, Optional, Type
from contextlib import asynccontextmanager

from .interfaces import (
    DataSource, SearchStrategy, SearchQuery, SearchResult, 
    SearchType, DataSourceType, ResultRanker, FilterProcessor
)
from ..datasources.elasticsearch_source import ElasticsearchSource
from ..datasources.vector_source import VectorSource
from ..datasources.database_source import PostgreSQLSource
from ..strategies.keyword_strategy import KeywordSearchStrategy
from ..strategies.semantic_strategy import SemanticSearchStrategy
from ..strategies.hybrid_strategy import HybridSearchStrategy
from ..strategies.multi_stage_strategy import MultiStageSearchStrategy
from ..filters.filter_processor import UniversalFilterProcessor

logger = logging.getLogger(__name__)


class SearchManager:
    """混合检索管理器"""
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.data_sources: List[DataSource] = []
        self.strategies: Dict[SearchType, SearchStrategy] = {}
        self.filter_processor: FilterProcessor = None
        self.result_ranker: Optional[ResultRanker] = None
        self.initialized = False
        
        # 数据源类型映射
        self.data_source_classes = {
            DataSourceType.ELASTICSEARCH: ElasticsearchSource,
            DataSourceType.VECTOR_DB: VectorSource,
            DataSourceType.RELATIONAL_DB: PostgreSQLSource,
        }
        
        # 策略类型映射
        self.strategy_classes = {
            SearchType.KEYWORD: KeywordSearchStrategy,
            SearchType.SEMANTIC: SemanticSearchStrategy,
            SearchType.HYBRID: HybridSearchStrategy,
            'multi_stage': MultiStageSearchStrategy,  # 添加多阶段策略
        }
    
    async def initialize(self) -> bool:
        """初始化搜索管理器"""
        try:
            # 初始化数据源
            await self._initialize_data_sources()
            
            # 初始化检索策略
            await self._initialize_strategies()
            
            # 初始化筛选处理器
            self._initialize_filter_processor()
            
            # 初始化结果排序器
            self._initialize_result_ranker()
            
            self.initialized = True
            logger.info("Search manager initialized successfully")
            return True
            
        except Exception as e:
            logger.error(f"Failed to initialize search manager: {e}")
            return False
    
    async def _initialize_data_sources(self):
        """初始化数据源"""
        data_sources_config = self.config.get('data_sources', [])
        
        for ds_config in data_sources_config:
            try:
                ds_type = DataSourceType(ds_config.get('type'))
                ds_class = self.data_source_classes.get(ds_type)
                
                if not ds_class:
                    logger.warning(f"Unsupported data source type: {ds_type}")
                    continue
                
                # 创建数据源实例
                data_source = ds_class(ds_config)
                
                # 连接数据源
                if await data_source.connect():
                    self.data_sources.append(data_source)
                    logger.info(f"Data source {data_source.name} connected successfully")
                else:
                    logger.error(f"Failed to connect data source: {data_source.name}")
                    
            except Exception as e:
                logger.error(f"Failed to initialize data source {ds_config.get('name', 'unknown')}: {e}")
    
    async def _initialize_strategies(self):
        """初始化检索策略"""
        strategies_config = self.config.get('strategies', {})
        
        for strategy_type, strategy_config in strategies_config.items():
            try:
                # 处理标准搜索类型
                if strategy_type in ['keyword', 'semantic', 'hybrid']:
                    search_type = SearchType(strategy_type)
                    strategy_class = self.strategy_classes.get(search_type)

                    if not strategy_class:
                        logger.warning(f"Unsupported strategy type: {search_type}")
                        continue

                    # 创建策略实例
                    strategy = strategy_class(strategy_config)

                    # 对于语义策略，需要初始化模型
                    if isinstance(strategy, SemanticSearchStrategy):
                        await strategy.initialize()

                    self.strategies[search_type] = strategy
                    logger.info(f"Strategy {search_type.value} initialized successfully")

                # 处理多阶段策略
                elif strategy_type == 'multi_stage':
                    strategy_class = self.strategy_classes.get('multi_stage')
                    if strategy_class:
                        strategy = strategy_class(strategy_config)
                        # 多阶段策略使用特殊键
                        self.strategies['multi_stage'] = strategy
                        logger.info("Multi-stage strategy initialized successfully")
                    else:
                        logger.warning("Multi-stage strategy class not found")

                else:
                    logger.warning(f"Unknown strategy type: {strategy_type}")

            except Exception as e:
                logger.error(f"Failed to initialize strategy {strategy_type}: {e}")
    
    def _initialize_filter_processor(self):
        """初始化筛选处理器"""
        filter_config = self.config.get('filter_processor', {})
        self.filter_processor = UniversalFilterProcessor(filter_config)
        logger.info("Filter processor initialized")
    
    def _initialize_result_ranker(self):
        """初始化结果排序器"""
        # 可以在这里实现自定义的结果排序器
        ranker_config = self.config.get('result_ranker')
        if ranker_config:
            # TODO: 实现自定义排序器
            pass
        logger.info("Result ranker initialized")
    
    async def search(self, query: SearchQuery) -> List[SearchResult]:
        """执行混合检索"""
        if not self.initialized:
            raise RuntimeError("Search manager not initialized")
        
        if not self.data_sources:
            logger.warning("No data sources available")
            return []
        
        try:
            # 获取检索策略
            strategy = self.strategies.get(query.search_type)
            if not strategy:
                logger.error(f"Strategy not found for type: {query.search_type}")
                return []
            
            # 应用筛选条件
            if self.filter_processor and query.filters:
                # 这里可以预处理筛选条件
                pass
            
            # 执行检索
            results = await strategy.search(self.data_sources, query)
            
            # 后处理结果
            processed_results = await self._post_process_results(results, query)
            
            logger.info(f"Search completed: {len(processed_results)} results found")
            return processed_results
            
        except Exception as e:
            logger.error(f"Search failed: {e}")
            return []

    async def multi_stage_search(self, query: SearchQuery) -> List[SearchResult]:
        """执行多阶段检索"""
        if not self.initialized:
            raise RuntimeError("Search manager not initialized")

        try:
            # 获取多阶段策略
            multi_stage_strategy = self.strategies.get('multi_stage')
            if not multi_stage_strategy:
                logger.warning("Multi-stage strategy not available, falling back to regular search")
                return await self.search(query)

            # 执行多阶段检索
            results = await multi_stage_strategy.search(self.data_sources, query)

            # 后处理结果
            processed_results = await self._post_process_results(results, query)

            logger.info(f"Multi-stage search completed: {len(processed_results)} results found")
            return processed_results

        except Exception as e:
            logger.error(f"Multi-stage search failed: {e}")
            # 降级到常规搜索
            return await self.search(query)
    
    async def _post_process_results(self, results: List[SearchResult], 
                                  query: SearchQuery) -> List[SearchResult]:
        """后处理检索结果"""
        if not results:
            return results
        
        # 去重
        deduplicated_results = self._deduplicate_results(results)
        
        # 自定义排序
        if self.result_ranker:
            ranked_results = self.result_ranker.rank(deduplicated_results, query)
        else:
            ranked_results = deduplicated_results
        
        # 应用分数阈值
        filtered_results = [r for r in ranked_results if r.score >= query.min_score]
        
        return filtered_results
    
    def _deduplicate_results(self, results: List[SearchResult]) -> List[SearchResult]:
        """去重检索结果"""
        seen = set()
        deduplicated = []
        
        for result in results:
            # 使用内容的哈希值进行去重
            content_hash = hash(result.content.strip().lower())
            
            if content_hash not in seen:
                seen.add(content_hash)
                deduplicated.append(result)
            else:
                # 如果内容重复，保留分数更高的结果
                for i, existing in enumerate(deduplicated):
                    if hash(existing.content.strip().lower()) == content_hash:
                        if result.score > existing.score:
                            deduplicated[i] = result
                        break
        
        return deduplicated
    
    async def health_check(self) -> Dict[str, Any]:
        """健康检查"""
        health_status = {
            'initialized': self.initialized,
            'data_sources': {},
            'strategies': list(self.strategies.keys()),
            'overall_status': 'healthy'
        }
        
        # 检查数据源健康状态
        for data_source in self.data_sources:
            try:
                is_healthy = await data_source.health_check()
                health_status['data_sources'][data_source.name] = {
                    'status': 'healthy' if is_healthy else 'unhealthy',
                    'type': data_source.source_type
                }
                
                if not is_healthy:
                    health_status['overall_status'] = 'degraded'
                    
            except Exception as e:
                health_status['data_sources'][data_source.name] = {
                    'status': 'error',
                    'error': str(e)
                }
                health_status['overall_status'] = 'degraded'
        
        return health_status
    
    async def add_data_source(self, config: Dict[str, Any]) -> bool:
        """动态添加数据源"""
        try:
            ds_type = DataSourceType(config.get('type'))
            ds_class = self.data_source_classes.get(ds_type)
            
            if not ds_class:
                logger.error(f"Unsupported data source type: {ds_type}")
                return False
            
            data_source = ds_class(config)
            
            if await data_source.connect():
                self.data_sources.append(data_source)
                logger.info(f"Data source {data_source.name} added successfully")
                return True
            else:
                logger.error(f"Failed to connect new data source: {data_source.name}")
                return False
                
        except Exception as e:
            logger.error(f"Failed to add data source: {e}")
            return False
    
    async def remove_data_source(self, name: str) -> bool:
        """移除数据源"""
        try:
            for i, data_source in enumerate(self.data_sources):
                if data_source.name == name:
                    await data_source.disconnect()
                    self.data_sources.pop(i)
                    logger.info(f"Data source {name} removed successfully")
                    return True
            
            logger.warning(f"Data source {name} not found")
            return False
            
        except Exception as e:
            logger.error(f"Failed to remove data source {name}: {e}")
            return False
    
    @asynccontextmanager
    async def get_manager(self):
        """上下文管理器"""
        try:
            if not self.initialized:
                await self.initialize()
            yield self
        finally:
            await self.cleanup()
    
    async def cleanup(self):
        """清理资源"""
        try:
            # 断开所有数据源连接
            for data_source in self.data_sources:
                await data_source.disconnect()
            
            logger.info("Search manager cleanup completed")
            
        except Exception as e:
            logger.error(f"Cleanup failed: {e}")
