"""
结构化数据筛选处理器实现
"""
import logging
from typing import Dict, Any, List, Union
from datetime import datetime, date
from enum import Enum

from ..core.interfaces import FilterProcessor, SearchQuery, SearchFilter

logger = logging.getLogger(__name__)


class FilterOperator(Enum):
    """筛选操作符枚举"""
    EQ = "eq"           # 等于
    NE = "ne"           # 不等于
    GT = "gt"           # 大于
    LT = "lt"           # 小于
    GTE = "gte"         # 大于等于
    LTE = "lte"         # 小于等于
    IN = "in"           # 包含在列表中
    NOT_IN = "not_in"   # 不包含在列表中
    CONTAINS = "contains"  # 包含字符串
    STARTS_WITH = "starts_with"  # 以...开始
    ENDS_WITH = "ends_with"      # 以...结束
    BETWEEN = "between"          # 在范围内
    IS_NULL = "is_null"          # 为空
    IS_NOT_NULL = "is_not_null"  # 不为空


class UniversalFilterProcessor(FilterProcessor):
    """通用筛选处理器"""
    
    def __init__(self, config: Dict[str, Any] = None):
        self.config = config or {}
        self.field_mappings = self.config.get('field_mappings', {})
        self.type_converters = {
            'datetime': self._convert_datetime,
            'date': self._convert_date,
            'int': int,
            'float': float,
            'bool': self._convert_bool,
            'str': str
        }
    
    def apply_filters(self, query: SearchQuery, data_source_query: Dict[str, Any]) -> Dict[str, Any]:
        """应用筛选条件到数据源查询"""
        if not query.filters:
            return data_source_query
        
        processed_filters = []
        for filter_condition in query.filters:
            processed_filter = self._process_single_filter(filter_condition)
            if processed_filter:
                processed_filters.append(processed_filter)
        
        if processed_filters:
            data_source_query['filters'] = processed_filters
        
        return data_source_query
    
    def _process_single_filter(self, filter_condition: SearchFilter) -> Dict[str, Any]:
        """处理单个筛选条件"""
        try:
            # 字段映射
            field = self.field_mappings.get(filter_condition.field, filter_condition.field)
            
            # 值转换
            value = self._convert_value(filter_condition.field, filter_condition.value)
            
            # 操作符验证
            if not self._validate_operator(filter_condition.operator):
                logger.warning(f"Invalid operator: {filter_condition.operator}")
                return None
            
            return {
                'field': field,
                'operator': filter_condition.operator,
                'value': value,
                'original_field': filter_condition.field
            }
            
        except Exception as e:
            logger.error(f"Failed to process filter {filter_condition.field}: {e}")
            return None
    
    def _convert_value(self, field: str, value: Any) -> Any:
        """转换筛选值"""
        # 获取字段类型配置
        field_config = self.config.get('field_types', {}).get(field, {})
        field_type = field_config.get('type', 'str')
        
        if isinstance(value, list):
            return [self._convert_single_value(field_type, v) for v in value]
        else:
            return self._convert_single_value(field_type, value)
    
    def _convert_single_value(self, field_type: str, value: Any) -> Any:
        """转换单个值"""
        if value is None:
            return None
        
        converter = self.type_converters.get(field_type, str)
        try:
            return converter(value)
        except Exception as e:
            logger.warning(f"Failed to convert value {value} to {field_type}: {e}")
            return value
    
    def _convert_datetime(self, value: Union[str, datetime]) -> datetime:
        """转换日期时间"""
        if isinstance(value, datetime):
            return value
        
        if isinstance(value, str):
            # 尝试多种日期格式
            formats = [
                '%Y-%m-%d %H:%M:%S',
                '%Y-%m-%dT%H:%M:%S',
                '%Y-%m-%d',
                '%Y/%m/%d %H:%M:%S',
                '%Y/%m/%d'
            ]
            
            for fmt in formats:
                try:
                    return datetime.strptime(value, fmt)
                except ValueError:
                    continue
            
            raise ValueError(f"Unable to parse datetime: {value}")
        
        raise TypeError(f"Invalid datetime type: {type(value)}")
    
    def _convert_date(self, value: Union[str, date, datetime]) -> date:
        """转换日期"""
        if isinstance(value, date):
            return value
        
        if isinstance(value, datetime):
            return value.date()
        
        if isinstance(value, str):
            dt = self._convert_datetime(value)
            return dt.date()
        
        raise TypeError(f"Invalid date type: {type(value)}")
    
    def _convert_bool(self, value: Union[str, bool, int]) -> bool:
        """转换布尔值"""
        if isinstance(value, bool):
            return value
        
        if isinstance(value, int):
            return bool(value)
        
        if isinstance(value, str):
            return value.lower() in ('true', '1', 'yes', 'on')
        
        return bool(value)
    
    def _validate_operator(self, operator: str) -> bool:
        """验证操作符"""
        try:
            FilterOperator(operator)
            return True
        except ValueError:
            return False
    
    def build_elasticsearch_filters(self, filters: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """构建Elasticsearch筛选条件"""
        es_filters = []
        
        for filter_data in filters:
            field = filter_data['field']
            operator = filter_data['operator']
            value = filter_data['value']
            
            es_filter = None
            
            if operator == FilterOperator.EQ.value:
                es_filter = {"term": {field: value}}
            elif operator == FilterOperator.NE.value:
                es_filter = {"bool": {"must_not": {"term": {field: value}}}}
            elif operator == FilterOperator.GT.value:
                es_filter = {"range": {field: {"gt": value}}}
            elif operator == FilterOperator.LT.value:
                es_filter = {"range": {field: {"lt": value}}}
            elif operator == FilterOperator.GTE.value:
                es_filter = {"range": {field: {"gte": value}}}
            elif operator == FilterOperator.LTE.value:
                es_filter = {"range": {field: {"lte": value}}}
            elif operator == FilterOperator.IN.value:
                es_filter = {"terms": {field: value}}
            elif operator == FilterOperator.NOT_IN.value:
                es_filter = {"bool": {"must_not": {"terms": {field: value}}}}
            elif operator == FilterOperator.CONTAINS.value:
                es_filter = {"wildcard": {field: f"*{value}*"}}
            elif operator == FilterOperator.STARTS_WITH.value:
                es_filter = {"prefix": {field: value}}
            elif operator == FilterOperator.BETWEEN.value and isinstance(value, list) and len(value) == 2:
                es_filter = {"range": {field: {"gte": value[0], "lte": value[1]}}}
            elif operator == FilterOperator.IS_NULL.value:
                es_filter = {"bool": {"must_not": {"exists": {"field": field}}}}
            elif operator == FilterOperator.IS_NOT_NULL.value:
                es_filter = {"exists": {"field": field}}
            
            if es_filter:
                es_filters.append(es_filter)
        
        return es_filters
    
    def build_sql_conditions(self, filters: List[Dict[str, Any]]) -> tuple:
        """构建SQL WHERE条件"""
        conditions = []
        params = []
        
        for filter_data in filters:
            field = filter_data['field']
            operator = filter_data['operator']
            value = filter_data['value']
            
            param_placeholder = f"${len(params) + 1}"
            
            if operator == FilterOperator.EQ.value:
                conditions.append(f"{field} = {param_placeholder}")
                params.append(value)
            elif operator == FilterOperator.NE.value:
                conditions.append(f"{field} != {param_placeholder}")
                params.append(value)
            elif operator == FilterOperator.GT.value:
                conditions.append(f"{field} > {param_placeholder}")
                params.append(value)
            elif operator == FilterOperator.LT.value:
                conditions.append(f"{field} < {param_placeholder}")
                params.append(value)
            elif operator == FilterOperator.GTE.value:
                conditions.append(f"{field} >= {param_placeholder}")
                params.append(value)
            elif operator == FilterOperator.LTE.value:
                conditions.append(f"{field} <= {param_placeholder}")
                params.append(value)
            elif operator == FilterOperator.IN.value and isinstance(value, list):
                placeholders = ",".join([f"${len(params) + i + 1}" for i in range(len(value))])
                conditions.append(f"{field} IN ({placeholders})")
                params.extend(value)
            elif operator == FilterOperator.CONTAINS.value:
                conditions.append(f"{field} ILIKE {param_placeholder}")
                params.append(f"%{value}%")
            elif operator == FilterOperator.STARTS_WITH.value:
                conditions.append(f"{field} ILIKE {param_placeholder}")
                params.append(f"{value}%")
            elif operator == FilterOperator.ENDS_WITH.value:
                conditions.append(f"{field} ILIKE {param_placeholder}")
                params.append(f"%{value}")
            elif operator == FilterOperator.BETWEEN.value and isinstance(value, list) and len(value) == 2:
                conditions.append(f"{field} BETWEEN {param_placeholder} AND ${len(params) + 2}")
                params.extend(value)
            elif operator == FilterOperator.IS_NULL.value:
                conditions.append(f"{field} IS NULL")
            elif operator == FilterOperator.IS_NOT_NULL.value:
                conditions.append(f"{field} IS NOT NULL")
        
        return conditions, params
    
    def build_mongodb_filters(self, filters: List[Dict[str, Any]]) -> Dict[str, Any]:
        """构建MongoDB筛选条件"""
        mongo_filter = {}
        
        for filter_data in filters:
            field = filter_data['field']
            operator = filter_data['operator']
            value = filter_data['value']
            
            if operator == FilterOperator.EQ.value:
                mongo_filter[field] = value
            elif operator == FilterOperator.NE.value:
                mongo_filter[field] = {"$ne": value}
            elif operator == FilterOperator.GT.value:
                mongo_filter[field] = {"$gt": value}
            elif operator == FilterOperator.LT.value:
                mongo_filter[field] = {"$lt": value}
            elif operator == FilterOperator.GTE.value:
                mongo_filter[field] = {"$gte": value}
            elif operator == FilterOperator.LTE.value:
                mongo_filter[field] = {"$lte": value}
            elif operator == FilterOperator.IN.value:
                mongo_filter[field] = {"$in": value}
            elif operator == FilterOperator.NOT_IN.value:
                mongo_filter[field] = {"$nin": value}
            elif operator == FilterOperator.CONTAINS.value:
                mongo_filter[field] = {"$regex": f".*{value}.*", "$options": "i"}
            elif operator == FilterOperator.STARTS_WITH.value:
                mongo_filter[field] = {"$regex": f"^{value}", "$options": "i"}
            elif operator == FilterOperator.ENDS_WITH.value:
                mongo_filter[field] = {"$regex": f"{value}$", "$options": "i"}
            elif operator == FilterOperator.BETWEEN.value and isinstance(value, list) and len(value) == 2:
                mongo_filter[field] = {"$gte": value[0], "$lte": value[1]}
            elif operator == FilterOperator.IS_NULL.value:
                mongo_filter[field] = None
            elif operator == FilterOperator.IS_NOT_NULL.value:
                mongo_filter[field] = {"$ne": None}
        
        return mongo_filter
