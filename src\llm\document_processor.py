"""
大模型驱动的文档预处理器
"""
import logging
import asyncio
import re
from typing import List, Dict, Any, Optional, Tuple
from dataclasses import dataclass
from abc import ABC, abstractmethod

import tiktoken
from langchain.text_splitter import RecursiveCharacterTextSplitter
from langchain.schema import Document
from langchain_openai import ChatOpenAI
from langchain.prompts import PromptTemplate
from .model_adapter import create_model_adapter, BaseModelAdapter

logger = logging.getLogger(__name__)


@dataclass
class DocumentChunk:
    """文档块数据结构"""
    content: str
    metadata: Dict[str, Any]
    chunk_id: str
    parent_doc_id: str
    chunk_index: int
    summary: Optional[str] = None
    keywords: Optional[List[str]] = None
    semantic_tags: Optional[List[str]] = None


class LLMDocumentProcessor:
    """大模型驱动的文档处理器"""
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config

        # 使用模型适配器支持多种模型
        provider = config.get('provider', 'openai')
        if provider == 'openai' and 'provider' not in config:
            # 向后兼容：如果没有指定provider，使用原来的ChatOpenAI
            self.llm = ChatOpenAI(
                model=config.get('model', 'gpt-3.5-turbo'),
                temperature=config.get('temperature', 0.1),
                max_tokens=config.get('max_tokens', 1000)
            )
            self.use_adapter = False
        else:
            # 使用新的模型适配器
            self.llm = create_model_adapter(config)
            self.use_adapter = True
        
        # 文档分块器
        self.text_splitter = RecursiveCharacterTextSplitter(
            chunk_size=config.get('chunk_size', 1000),
            chunk_overlap=config.get('chunk_overlap', 200),
            length_function=self._count_tokens,
            separators=["\n\n", "\n", "。", "！", "？", ".", "!", "?", " ", ""]
        )
        
        # Token计数器
        self.encoding = tiktoken.get_encoding("cl100k_base")
        
        # 提示模板
        self._init_prompts()
    
    def _init_prompts(self):
        """初始化提示模板"""
        # 摘要生成提示
        self.summary_prompt = PromptTemplate(
            input_variables=["content"],
            template="""请为以下文档内容生成一个简洁的摘要（不超过100字）：

文档内容：
{content}

摘要："""
        )
        
        # 关键词提取提示
        self.keywords_prompt = PromptTemplate(
            input_variables=["content"],
            template="""请从以下文档内容中提取5-10个最重要的关键词，用逗号分隔：

文档内容：
{content}

关键词："""
        )
        
        # 语义标签提示
        self.tags_prompt = PromptTemplate(
            input_variables=["content"],
            template="""请为以下文档内容生成3-5个语义标签，用于分类和检索，用逗号分隔：

文档内容：
{content}

语义标签："""
        )
        
        # 智能分块提示
        self.chunk_prompt = PromptTemplate(
            input_variables=["content"],
            template="""请分析以下文档内容的结构，建议最佳的分块策略。考虑语义完整性、主题连贯性：

文档内容：
{content}

分块建议："""
        )
    
    def _count_tokens(self, text: str) -> int:
        """计算文本的token数量"""
        return len(self.encoding.encode(text))
    
    async def process_document(self, document: Dict[str, Any]) -> List[DocumentChunk]:
        """处理单个文档"""
        doc_id = document.get('id', 'unknown')
        content = document.get('content', '')
        
        if not content.strip():
            logger.warning(f"Document {doc_id} has empty content")
            return []
        
        try:
            # 1. 智能分块
            chunks = await self._intelligent_chunk(content, doc_id)
            
            # 2. 并行处理每个块
            tasks = []
            for i, chunk in enumerate(chunks):
                task = self._process_chunk(chunk, i, doc_id, document.get('metadata', {}))
                tasks.append(task)
            
            processed_chunks = await asyncio.gather(*tasks, return_exceptions=True)
            
            # 过滤异常结果
            valid_chunks = []
            for chunk in processed_chunks:
                if isinstance(chunk, DocumentChunk):
                    valid_chunks.append(chunk)
                else:
                    logger.error(f"Failed to process chunk: {chunk}")
            
            logger.info(f"Processed document {doc_id} into {len(valid_chunks)} chunks")
            return valid_chunks
            
        except Exception as e:
            logger.error(f"Failed to process document {doc_id}: {e}")
            return []
    
    async def _intelligent_chunk(self, content: str, doc_id: str) -> List[str]:
        """智能文档分块"""
        # 首先使用基础分块器
        base_chunks = self.text_splitter.split_text(content)
        
        # 如果文档较短，直接返回
        if len(base_chunks) <= 2:
            return base_chunks
        
        try:
            # 使用LLM分析文档结构，优化分块
            chunk_analysis = await self._analyze_document_structure(content)
            
            # 根据分析结果调整分块策略
            if chunk_analysis and "建议" in chunk_analysis:
                # 这里可以根据LLM的建议调整分块参数
                # 简化实现：如果LLM建议更小的块，减少chunk_size
                if "更小" in chunk_analysis or "细分" in chunk_analysis:
                    smaller_splitter = RecursiveCharacterTextSplitter(
                        chunk_size=self.config.get('chunk_size', 1000) // 2,
                        chunk_overlap=self.config.get('chunk_overlap', 200),
                        length_function=self._count_tokens
                    )
                    return smaller_splitter.split_text(content)
            
        except Exception as e:
            logger.warning(f"LLM chunk analysis failed for {doc_id}: {e}")
        
        return base_chunks
    
    async def _analyze_document_structure(self, content: str) -> Optional[str]:
        """分析文档结构"""
        try:
            # 限制内容长度以避免token超限
            if self._count_tokens(content) > 2000:
                content = content[:3000]  # 截取前3000字符
            
            prompt = self.chunk_prompt.format(content=content)
            response = await self.llm.ainvoke(prompt)
            return response.content
            
        except Exception as e:
            logger.error(f"Document structure analysis failed: {e}")
            return None
    
    async def _process_chunk(self, content: str, index: int, doc_id: str, 
                           base_metadata: Dict[str, Any]) -> DocumentChunk:
        """处理单个文档块"""
        chunk_id = f"{doc_id}_chunk_{index}"
        
        # 并行执行摘要、关键词和标签提取
        tasks = [
            self._generate_summary(content),
            self._extract_keywords(content),
            self._generate_semantic_tags(content)
        ]
        
        try:
            summary, keywords, tags = await asyncio.gather(*tasks, return_exceptions=True)
            
            # 处理异常结果
            if isinstance(summary, Exception):
                logger.warning(f"Summary generation failed for {chunk_id}: {summary}")
                summary = None
            
            if isinstance(keywords, Exception):
                logger.warning(f"Keywords extraction failed for {chunk_id}: {keywords}")
                keywords = []
            
            if isinstance(tags, Exception):
                logger.warning(f"Tags generation failed for {chunk_id}: {tags}")
                tags = []
            
            # 构建元数据
            metadata = {
                **base_metadata,
                'chunk_index': index,
                'token_count': self._count_tokens(content),
                'char_count': len(content)
            }
            
            return DocumentChunk(
                content=content,
                metadata=metadata,
                chunk_id=chunk_id,
                parent_doc_id=doc_id,
                chunk_index=index,
                summary=summary,
                keywords=keywords if isinstance(keywords, list) else [],
                semantic_tags=tags if isinstance(tags, list) else []
            )
            
        except Exception as e:
            logger.error(f"Failed to process chunk {chunk_id}: {e}")
            # 返回基础版本
            return DocumentChunk(
                content=content,
                metadata=base_metadata,
                chunk_id=chunk_id,
                parent_doc_id=doc_id,
                chunk_index=index
            )
    
    async def _generate_summary(self, content: str) -> Optional[str]:
        """生成摘要"""
        try:
            prompt = self.summary_prompt.format(content=content[:2000])
            response = await self.llm.ainvoke(prompt)
            return response.content.strip()
        except Exception as e:
            logger.error(f"Summary generation failed: {e}")
            return None
    
    async def _extract_keywords(self, content: str) -> List[str]:
        """提取关键词"""
        try:
            prompt = self.keywords_prompt.format(content=content[:2000])
            response = await self.llm.ainvoke(prompt)
            keywords_text = response.content.strip()
            
            # 解析关键词
            keywords = [kw.strip() for kw in keywords_text.split(',')]
            return [kw for kw in keywords if kw]
            
        except Exception as e:
            logger.error(f"Keywords extraction failed: {e}")
            return []
    
    async def _generate_semantic_tags(self, content: str) -> List[str]:
        """生成语义标签"""
        try:
            prompt = self.tags_prompt.format(content=content[:2000])
            response = await self.llm.ainvoke(prompt)
            tags_text = response.content.strip()
            
            # 解析标签
            tags = [tag.strip() for tag in tags_text.split(',')]
            return [tag for tag in tags if tag]
            
        except Exception as e:
            logger.error(f"Semantic tags generation failed: {e}")
            return []
    
    async def batch_process_documents(self, documents: List[Dict[str, Any]], 
                                    batch_size: int = 5) -> List[DocumentChunk]:
        """批量处理文档"""
        all_chunks = []
        
        for i in range(0, len(documents), batch_size):
            batch = documents[i:i + batch_size]
            
            # 并行处理批次内的文档
            tasks = [self.process_document(doc) for doc in batch]
            batch_results = await asyncio.gather(*tasks, return_exceptions=True)
            
            # 收集结果
            for result in batch_results:
                if isinstance(result, list):
                    all_chunks.extend(result)
                else:
                    logger.error(f"Batch processing error: {result}")
            
            logger.info(f"Processed batch {i//batch_size + 1}/{(len(documents)-1)//batch_size + 1}")
        
        return all_chunks
