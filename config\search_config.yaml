# 混合检索系统配置文件

# 数据源配置
data_sources:
  - name: "elasticsearch_main"
    type: "elasticsearch"
    host: "localhost"
    port: 9200
    index_name: "documents"
    username: null
    password: null
    enabled: true
    config:
      timeout: 30
      max_retries: 3

  - name: "vector_store"
    type: "vector_db"
    model_name: "all-MiniLM-L6-v2"
    enabled: true
    config:
      dimension: 384
      similarity_metric: "cosine"

  - name: "postgres_db"
    type: "relational_db"
    host: "localhost"
    port: 5432
    database: "search_db"
    username: "postgres"
    password: "password"
    table_name: "documents"
    content_field: "content"
    id_field: "id"
    enabled: false
    config:
      pool_size: 10
      timeout: 30

# LLM增强功能配置
llm:
  # 文档处理器配置
  document_processor:
    provider: "openai"  # openai, anthropic, azure_openai, ollama, zhipu, qwen, baidu
    model: "gpt-3.5-turbo"
    temperature: 0.1
    max_tokens: 1000
    chunk_size: 1000
    chunk_overlap: 200
    # API配置（可选，也可通过环境变量设置）
    # api_key: "your-api-key"
    # base_url: "https://api.openai.com/v1"

  # 查询处理器配置
  query_processor:
    provider: "openai"
    model: "gpt-3.5-turbo"
    temperature: 0.1
    max_tokens: 1000

  # 结果优化器配置
  result_optimizer:
    provider: "openai"
    model: "gpt-3.5-turbo"
    temperature: 0.1
    max_tokens: 1500
    relevance_weight: 0.6
    quality_weight: 0.4

  # 功能开关
  enable_query_enhancement: true
  enable_result_optimization: true
  enable_answer_generation: true

  # 多模型配置示例
  alternative_configs:
    # Anthropic Claude配置
    anthropic:
      provider: "anthropic"
      model: "claude-3-sonnet-20240229"
      temperature: 0.1
      max_tokens: 1000

    # Azure OpenAI配置
    azure:
      provider: "azure_openai"
      model: "gpt-35-turbo"
      deployment_name: "gpt-35-turbo"
      api_version: "2023-12-01-preview"
      temperature: 0.1
      max_tokens: 1000

    # 本地Ollama配置
    ollama:
      provider: "ollama"
      model: "llama2"
      base_url: "http://localhost:11434"
      temperature: 0.1

    # 兼容OpenAI接口的其他模型
    compatible_api:
      provider: "openai"
      model: "qwen-turbo"  # 或其他兼容模型
      base_url: "https://dashscope.aliyuncs.com/compatible-mode/v1"  # 通义千问兼容接口
      temperature: 0.1
      max_tokens: 1000

# 检索策略配置
strategies:
  keyword:
    enabled: true
    config:
      boost_weights:
        elasticsearch_main: 1.2
        postgres_db: 1.0
      merge_method: "score_weighted"  # simple_concat, score_weighted, round_robin

  semantic:
    enabled: true
    config:
      model_name: "all-MiniLM-L6-v2"
      semantic_weight: 1.0
      similarity_threshold: 0.3

  hybrid:
    enabled: true
    config:
      keyword_weight: 0.4
      semantic_weight: 0.6
      fusion_method: "rrf"  # rrf, linear, max
      rrf_k: 60
      keyword_config:
        boost_weights:
          elasticsearch_main: 1.2
          vector_store: 1.0
        merge_method: "score_weighted"
      semantic_config:
        model_name: "all-MiniLM-L6-v2"
        semantic_weight: 1.0
        similarity_threshold: 0.3

  # 多阶段检索策略
  multi_stage:
    enabled: true
    config:
      # 第一阶段：召回配置
      recall_limit: 100
      recall_strategies: ["keyword", "semantic", "hybrid"]
      enable_query_expansion: true
      enable_negative_sampling: true

      # 第二阶段：重排序配置
      reranker_type: "bge"  # bge, cross_encoder, colbert, cohere
      reranker_config:
        model_name: "BAAI/bge-reranker-large"
        batch_size: 32
        max_length: 512
        device: "cpu"  # cpu, cuda

      final_limit: 10

      # 融合配置
      fusion_method: "weighted"  # weighted, max, rerank_only
      recall_weight: 0.3
      rerank_weight: 0.7

      # 查询处理器配置
      query_processor:
        model: "gpt-3.5-turbo"
        temperature: 0.1
        max_tokens: 1000

# 召回率优化配置
recall_optimizer:
  enable_query_expansion: true
  max_expanded_queries: 5
  expansion_weight: 0.8

  enable_multi_model: true
  model_weights:
    keyword: 1.0
    semantic: 1.0
    hybrid: 1.2

  candidate_expansion_factor: 3
  min_candidate_score: 0.1

  enable_negative_sampling: true
  negative_ratio: 0.2

  enable_query_rewriting: true
  rewrite_strategies: ["synonym", "paraphrase"]

  query_processor:
    model: "gpt-3.5-turbo"
    temperature: 0.1

# 重排序管道配置
rerank_pipeline:
  # 单个reranker配置
  single_reranker:
    type: "bge"
    model_name: "BAAI/bge-reranker-large"
    batch_size: 32
    max_length: 512
    device: "cpu"

  # 多reranker融合配置
  multi_reranker:
    enabled: false
    rerankers:
      - type: "bge"
        model_name: "BAAI/bge-reranker-large"
        batch_size: 16
        device: "cpu"
      - type: "cross_encoder"
        model_name: "cross-encoder/ms-marco-MiniLM-L-6-v2"
        device: "cpu"

    fusion_method: "weighted_average"  # weighted_average, rank_fusion, score_fusion, ensemble_voting, cascaded
    weights: [0.6, 0.4]
    top_k: 10
    enable_score_normalization: true
    enable_diversity: false
    diversity_lambda: 0.5

  # API-based reranker配置
  api_reranker:
    enabled: false
    type: "cohere"
    api_key: "${COHERE_API_KEY}"
    model_name: "rerank-english-v2.0"
    top_n: 100

# 筛选处理器配置
filter_processor:
  field_mappings:
    # 字段名映射，用于统一不同数据源的字段名
    create_time: "created_at"
    update_time: "updated_at"
    doc_type: "category"
  
  field_types:
    # 字段类型定义，用于值转换
    created_at:
      type: "datetime"
    updated_at:
      type: "datetime"
    category:
      type: "str"
    tags:
      type: "str"
    score:
      type: "float"
    priority:
      type: "int"
    active:
      type: "bool"
    publish_date:
      type: "date"

# 搜索配置
search:
  default_limit: 10
  max_limit: 100
  default_min_score: 0.0
  timeout_seconds: 30

# 日志配置
logging:
  level: "INFO"
  format: "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
  handlers:
    - type: "console"
    - type: "file"
      filename: "logs/search.log"
      max_bytes: 10485760  # 10MB
      backup_count: 5
