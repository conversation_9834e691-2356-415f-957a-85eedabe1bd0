# 大模型增强检索系统使用教程

## 快速开始

### 第一步：环境准备

1. **克隆项目**
```bash
git clone <repository-url>
cd search_chain
```

2. **安装依赖**
```bash
pip install -r requirements.txt
```

3. **配置API密钥**
```bash
export OPENAI_API_KEY="your-openai-api-key-here"
```

### 第二步：基础使用

创建 `quick_start.py`：

```python
import asyncio
import os
from src.llm.enhanced_search_manager import EnhancedSearchManager
from src.core.interfaces import SearchQuery, SearchType
from src.config.config_manager import ConfigManager

async def main():
    # 1. 初始化系统
    config = ConfigManager("config/search_config.yaml").get_config()
    search_manager = EnhancedSearchManager(config)
    await search_manager.initialize()
    
    # 2. 添加示例文档
    documents = [
        {
            'id': 'doc_1',
            'content': 'Python是一种高级编程语言，广泛用于数据科学和机器学习。',
            'metadata': {'category': 'programming', 'language': 'zh'}
        }
    ]
    
    await search_manager.add_documents_with_processing(documents)
    
    # 3. 执行搜索
    query = SearchQuery(
        query="Python编程语言的特点",
        search_type=SearchType.HYBRID,
        limit=5
    )
    
    response = await search_manager.enhanced_search(query)
    
    # 4. 查看结果
    print(f"找到 {response['total_results']} 个结果")
    if response.get('generated_answer'):
        print(f"答案: {response['generated_answer']['answer']}")
    
    await search_manager.cleanup()

if __name__ == "__main__":
    asyncio.run(main())
```

运行：
```bash
python quick_start.py
```

## 详细教程

### 1. 文档处理和索引

#### 1.1 基础文档添加

```python
from src.llm.enhanced_search_manager import EnhancedSearchManager

# 准备文档数据
documents = [
    {
        'id': 'tech_001',
        'content': '''
        人工智能（AI）是计算机科学的一个分支，致力于创建能够执行
        通常需要人类智能的任务的系统。AI包括机器学习、深度学习、
        自然语言处理、计算机视觉等多个子领域。
        ''',
        'metadata': {
            'category': 'technology',
            'subcategory': 'artificial_intelligence',
            'tags': ['AI', 'machine-learning', 'technology'],
            'author': 'Tech Expert',
            'created_at': '2024-01-01T10:00:00',
            'language': 'zh'
        }
    },
    {
        'id': 'prog_001',
        'content': '''
        Python是一种解释型、面向对象、动态数据类型的高级程序设计语言。
        Python语法简洁清晰，特色之一是强制用空白符作为语句缩进。
        Python具有丰富和强大的库，被称为胶水语言。
        ''',
        'metadata': {
            'category': 'programming',
            'subcategory': 'python',
            'tags': ['python', 'programming', 'language'],
            'author': 'Python Developer',
            'created_at': '2024-01-02T10:00:00',
            'language': 'zh'
        }
    }
]

# 添加文档（带LLM预处理）
search_manager = EnhancedSearchManager(config)
await search_manager.initialize()

success = await search_manager.add_documents_with_processing(documents)
if success:
    print("✅ 文档处理和索引完成")
```

#### 1.2 批量文档处理

```python
import json

# 从文件加载大量文档
def load_documents_from_file(file_path):
    with open(file_path, 'r', encoding='utf-8') as f:
        return json.load(f)

# 批量处理
documents = load_documents_from_file('data/documents.json')

# 分批处理以避免内存问题
batch_size = 10
for i in range(0, len(documents), batch_size):
    batch = documents[i:i + batch_size]
    success = await search_manager.add_documents_with_processing(batch)
    print(f"处理批次 {i//batch_size + 1}: {'成功' if success else '失败'}")
```

### 2. 智能搜索功能

#### 2.1 基础搜索

```python
from src.core.interfaces import SearchQuery, SearchType, SearchFilter

# 简单搜索
query = SearchQuery(
    query="人工智能的应用",
    search_type=SearchType.HYBRID,
    limit=5
)

response = await search_manager.enhanced_search(query)
print(f"找到 {response['total_results']} 个结果")
```

#### 2.2 高级搜索

```python
# 带筛选条件的搜索
filters = [
    SearchFilter(field="category", operator="eq", value="technology"),
    SearchFilter(field="created_at", operator="gte", value="2024-01-01T00:00:00")
]

query = SearchQuery(
    query="机器学习算法",
    search_type=SearchType.HYBRID,
    filters=filters,
    limit=10,
    min_score=0.3,
    boost_fields={"title": 2.0, "content": 1.0}
)

response = await search_manager.enhanced_search(query)
```

#### 2.3 不同搜索类型

```python
# 关键词搜索
keyword_query = SearchQuery(
    query="Python编程",
    search_type=SearchType.KEYWORD,
    limit=5
)

# 语义搜索
semantic_query = SearchQuery(
    query="如何学习编程？",
    search_type=SearchType.SEMANTIC,
    limit=5
)

# 混合搜索（推荐）
hybrid_query = SearchQuery(
    query="Python机器学习教程",
    search_type=SearchType.HYBRID,
    limit=5
)

# 执行搜索
responses = await asyncio.gather(
    search_manager.enhanced_search(keyword_query),
    search_manager.enhanced_search(semantic_query),
    search_manager.enhanced_search(hybrid_query)
)
```

### 3. 查询分析功能

#### 3.1 查询意图识别

```python
from src.llm.query_processor import LLMQueryProcessor

query_processor = LLMQueryProcessor({'model': 'gpt-3.5-turbo'})

# 分析不同类型的查询
queries = [
    "什么是机器学习？",           # 事实性查询
    "机器学习的工作原理",         # 概念性查询
    "如何实现机器学习算法？",     # 过程性查询
    "机器学习和深度学习的区别",   # 比较性查询
    "机器学习的优缺点分析"        # 分析性查询
]

for query_text in queries:
    analysis = await query_processor.analyze_query(query_text)
    print(f"查询: {query_text}")
    print(f"意图: {analysis.intent.value}")
    print(f"关键词: {', '.join(analysis.keywords)}")
    print(f"扩展查询: {', '.join(analysis.expanded_queries[:2])}")
    print("-" * 50)
```

#### 3.2 多语言查询处理

```python
# 中英文混合查询
mixed_query = "What is 机器学习 in Python?"
translated = await query_processor.process_multi_language_query(
    mixed_query, target_language='zh'
)
print(f"翻译后: {translated}")

# 英文查询翻译为中文
english_query = "How to implement machine learning algorithms?"
chinese_query = await query_processor.process_multi_language_query(
    english_query, target_language='zh'
)
print(f"中文查询: {chinese_query}")
```

### 4. 结果优化和答案生成

#### 4.1 结果质量评估

```python
from src.llm.result_optimizer import LLMResultOptimizer

result_optimizer = LLMResultOptimizer({
    'model': 'gpt-3.5-turbo',
    'relevance_weight': 0.6,
    'quality_weight': 0.4
})

# 获取搜索结果
query = SearchQuery(query="Python数据分析", limit=5)
response = await search_manager.enhanced_search(query)

# 查看优化结果
if 'optimized_results' in response:
    for i, opt_result in enumerate(response['optimized_results'][:3]):
        print(f"结果 {i+1}:")
        print(f"  相关性分数: {opt_result.relevance_score:.2f}")
        print(f"  质量分数: {opt_result.quality_score:.2f}")
        print(f"  最终分数: {opt_result.final_score:.2f}")
        print(f"  评估说明: {opt_result.explanation}")
        print()
```

#### 4.2 智能答案生成

```python
# 执行搜索并生成答案
query = SearchQuery(query="Python在数据科学中的优势是什么？")
response = await search_manager.enhanced_search(query)

if response.get('generated_answer'):
    answer_data = response['generated_answer']
    print("🤖 AI生成的答案:")
    print(answer_data['answer'])
    
    if answer_data.get('key_points'):
        print("\n📋 关键要点:")
        for i, point in enumerate(answer_data['key_points'], 1):
            print(f"  {i}. {point}")
```

### 5. 用户反馈和系统学习

#### 5.1 收集用户反馈

```python
from src.llm.feedback_system import FeedbackSystem, UserFeedback, FeedbackType, FeedbackRating
import uuid

feedback_system = FeedbackSystem({'db_path': 'data/feedback.db'})
await feedback_system.initialize()

# 模拟用户搜索和反馈流程
query = SearchQuery(query="Python机器学习库")
response = await search_manager.enhanced_search(query)

if response['results']:
    # 用户对第一个结果给出反馈
    feedback = UserFeedback(
        id=str(uuid.uuid4()),
        query=query.query,
        result_id=response['results'][0].id,
        feedback_type=FeedbackType.RELEVANCE,
        rating=FeedbackRating.GOOD,
        comment="结果很相关，找到了需要的信息",
        user_id="user_123",
        session_id=str(uuid.uuid4())
    )
    
    success = await feedback_system.collect_feedback(feedback)
    print(f"反馈收集: {'成功' if success else '失败'}")
```

#### 5.2 系统性能分析

```python
# 获取系统性能指标
metrics = await feedback_system.get_performance_metrics()

print("📊 系统性能指标:")
print(f"  总反馈数: {metrics.get('total_feedback', 0)}")
print(f"  平均评分: {metrics.get('average_rating', 0):.2f}/5.0")

# 查看需要改进的查询
worst_queries = metrics.get('worst_queries', [])
if worst_queries:
    print("\n⚠️ 需要改进的查询:")
    for query_info in worst_queries[:3]:
        print(f"  - {query_info['query']} (评分: {query_info['avg_rating']:.2f})")

# 获取优化建议
if worst_queries:
    suggestions = await feedback_system.get_query_optimization_suggestions(
        worst_queries[0]['query']
    )
    if suggestions['suggestions']:
        print(f"\n💡 优化建议:")
        for suggestion_type, suggestion_list in suggestions['suggestions'].items():
            print(f"  {suggestion_type}: {', '.join(suggestion_list[:2])}")
```

### 6. 搜索建议和自动完成

```python
# 获取搜索建议
partial_queries = ["机器", "Python编程", "数据分析"]

for partial in partial_queries:
    suggestions = await search_manager.get_search_suggestions(partial)
    print(f"'{partial}' 的搜索建议:")
    for i, suggestion in enumerate(suggestions[:3], 1):
        print(f"  {i}. {suggestion}")
    print()
```

### 7. 系统监控和分析

```python
# 获取系统分析数据
analytics = await search_manager.get_analytics()

print("🔍 搜索系统分析:")
print(f"  总查询数: {analytics.get('total_queries', 0)}")
print(f"  缓存命中率: {analytics.get('cache_hit_rate', 0):.1%}")

print("\n💾 数据源状态:")
for ds_status in analytics.get('data_sources_status', []):
    status_icon = "✅" if ds_status['healthy'] else "❌"
    print(f"  {status_icon} {ds_status['name']} ({ds_status['type']})")

# 获取学习洞察
insights = await feedback_system.get_recent_insights(5)
if insights:
    print(f"\n🧠 最近的学习洞察:")
    for insight in insights:
        print(f"  - {insight['pattern']} (置信度: {insight['confidence']:.2f})")
        print(f"    建议: {insight['recommendation'][:100]}...")
```

## 高级用法

### 1. 自定义配置

```python
# 创建自定义配置
custom_config = {
    'llm': {
        'document_processor': {
            'model': 'gpt-4',  # 使用更强的模型
            'temperature': 0.0,  # 更确定性的输出
            'chunk_size': 800,   # 更小的分块
        },
        'enable_query_enhancement': True,
        'enable_result_optimization': True,
        'enable_answer_generation': True
    }
}

search_manager = EnhancedSearchManager(custom_config)
```

### 2. 错误处理

```python
async def robust_search(search_manager, query_text):
    """带错误处理的搜索"""
    try:
        query = SearchQuery(query=query_text)
        response = await search_manager.enhanced_search(query)
        return response
    except ConnectionError as e:
        print(f"连接错误: {e}")
        # 尝试基础搜索
        return await search_manager.search(query)
    except ValueError as e:
        print(f"参数错误: {e}")
        return None
    except Exception as e:
        print(f"未知错误: {e}")
        return None

# 使用
response = await robust_search(search_manager, "测试查询")
```

### 3. 批量操作

```python
async def batch_search(search_manager, queries):
    """批量搜索"""
    tasks = []
    for query_text in queries:
        query = SearchQuery(query=query_text, limit=3)
        task = search_manager.enhanced_search(query)
        tasks.append(task)
    
    results = await asyncio.gather(*tasks, return_exceptions=True)
    
    # 处理结果
    successful_results = []
    for i, result in enumerate(results):
        if isinstance(result, Exception):
            print(f"查询 '{queries[i]}' 失败: {result}")
        else:
            successful_results.append(result)
    
    return successful_results

# 使用
queries = ["人工智能", "机器学习", "深度学习"]
results = await batch_search(search_manager, queries)
```

## 最佳实践

### 1. 性能优化
- 使用适当的批处理大小
- 启用缓存机制
- 合理设置超时时间
- 监控资源使用情况

### 2. 成本控制
- 选择合适的LLM模型
- 控制输入文本长度
- 使用缓存减少API调用
- 设置预算限制

### 3. 质量保证
- 收集用户反馈
- 定期评估结果质量
- 持续优化配置参数
- 监控系统性能指标

这个教程涵盖了系统的主要功能和使用方法，帮助您快速上手并充分利用大模型增强检索系统的强大功能。
