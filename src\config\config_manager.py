"""
配置管理器实现
"""
import os
import yaml
import json
import logging
from typing import Dict, Any, Optional, Union
from pathlib import Path
from pydantic import BaseModel, ValidationError

logger = logging.getLogger(__name__)


class DataSourceConfig(BaseModel):
    """数据源配置模型"""
    name: str
    type: str
    host: Optional[str] = "localhost"
    port: Optional[int] = None
    username: Optional[str] = None
    password: Optional[str] = None
    database: Optional[str] = None
    index_name: Optional[str] = None
    table_name: Optional[str] = None
    model_name: Optional[str] = None
    enabled: bool = True
    config: Dict[str, Any] = {}


class StrategyConfig(BaseModel):
    """策略配置模型"""
    enabled: bool = True
    config: Dict[str, Any] = {}


class FilterConfig(BaseModel):
    """筛选配置模型"""
    field_mappings: Dict[str, str] = {}
    field_types: Dict[str, Dict[str, Any]] = {}


class SearchConfig(BaseModel):
    """搜索配置模型"""
    default_limit: int = 10
    max_limit: int = 100
    default_min_score: float = 0.0
    timeout_seconds: int = 30


class ConfigManager:
    """配置管理器"""
    
    def __init__(self, config_path: Optional[str] = None):
        self.config_path = config_path or "config/search_config.yaml"
        self.config: Dict[str, Any] = {}
        self.data_sources: Dict[str, DataSourceConfig] = {}
        self.strategies: Dict[str, StrategyConfig] = {}
        self.filter_config: FilterConfig = FilterConfig()
        self.search_config: SearchConfig = SearchConfig()
        
    def load_config(self, config_path: Optional[str] = None) -> bool:
        """加载配置文件"""
        if config_path:
            self.config_path = config_path
        
        try:
            config_file = Path(self.config_path)
            
            if not config_file.exists():
                logger.warning(f"Config file not found: {self.config_path}, using defaults")
                self._load_default_config()
                return True
            
            # 根据文件扩展名选择解析器
            if config_file.suffix.lower() in ['.yaml', '.yml']:
                with open(config_file, 'r', encoding='utf-8') as f:
                    self.config = yaml.safe_load(f)
            elif config_file.suffix.lower() == '.json':
                with open(config_file, 'r', encoding='utf-8') as f:
                    self.config = json.load(f)
            else:
                raise ValueError(f"Unsupported config file format: {config_file.suffix}")
            
            # 解析配置
            self._parse_config()
            
            logger.info(f"Configuration loaded from: {self.config_path}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to load config: {e}")
            self._load_default_config()
            return False
    
    def _parse_config(self):
        """解析配置"""
        try:
            # 解析数据源配置
            data_sources_config = self.config.get('data_sources', [])
            for ds_config in data_sources_config:
                try:
                    ds = DataSourceConfig(**ds_config)
                    self.data_sources[ds.name] = ds
                except ValidationError as e:
                    logger.error(f"Invalid data source config {ds_config.get('name', 'unknown')}: {e}")
            
            # 解析策略配置
            strategies_config = self.config.get('strategies', {})
            for strategy_name, strategy_config in strategies_config.items():
                try:
                    strategy = StrategyConfig(**strategy_config)
                    self.strategies[strategy_name] = strategy
                except ValidationError as e:
                    logger.error(f"Invalid strategy config {strategy_name}: {e}")
            
            # 解析筛选配置
            filter_config = self.config.get('filter_processor', {})
            try:
                self.filter_config = FilterConfig(**filter_config)
            except ValidationError as e:
                logger.error(f"Invalid filter config: {e}")
                self.filter_config = FilterConfig()
            
            # 解析搜索配置
            search_config = self.config.get('search', {})
            try:
                self.search_config = SearchConfig(**search_config)
            except ValidationError as e:
                logger.error(f"Invalid search config: {e}")
                self.search_config = SearchConfig()
                
        except Exception as e:
            logger.error(f"Failed to parse config: {e}")
    
    def _load_default_config(self):
        """加载默认配置"""
        self.config = {
            'data_sources': [
                {
                    'name': 'elasticsearch_default',
                    'type': 'elasticsearch',
                    'host': 'localhost',
                    'port': 9200,
                    'index_name': 'documents',
                    'enabled': False
                },
                {
                    'name': 'vector_default',
                    'type': 'vector_db',
                    'model_name': 'all-MiniLM-L6-v2',
                    'enabled': True
                }
            ],
            'strategies': {
                'keyword': {
                    'enabled': True,
                    'config': {
                        'boost_weights': {},
                        'merge_method': 'score_weighted'
                    }
                },
                'semantic': {
                    'enabled': True,
                    'config': {
                        'model_name': 'all-MiniLM-L6-v2',
                        'semantic_weight': 1.0,
                        'similarity_threshold': 0.3
                    }
                },
                'hybrid': {
                    'enabled': True,
                    'config': {
                        'keyword_weight': 0.4,
                        'semantic_weight': 0.6,
                        'fusion_method': 'rrf',
                        'rrf_k': 60
                    }
                }
            },
            'filter_processor': {
                'field_mappings': {},
                'field_types': {
                    'created_at': {'type': 'datetime'},
                    'updated_at': {'type': 'datetime'},
                    'category': {'type': 'str'},
                    'tags': {'type': 'str'},
                    'score': {'type': 'float'},
                    'active': {'type': 'bool'}
                }
            },
            'search': {
                'default_limit': 10,
                'max_limit': 100,
                'default_min_score': 0.0,
                'timeout_seconds': 30
            }
        }
        
        self._parse_config()
        logger.info("Default configuration loaded")
    
    def save_config(self, config_path: Optional[str] = None) -> bool:
        """保存配置文件"""
        if config_path:
            self.config_path = config_path
        
        try:
            # 确保目录存在
            config_file = Path(self.config_path)
            config_file.parent.mkdir(parents=True, exist_ok=True)
            
            # 构建配置字典
            config_dict = self._build_config_dict()
            
            # 根据文件扩展名选择格式
            if config_file.suffix.lower() in ['.yaml', '.yml']:
                with open(config_file, 'w', encoding='utf-8') as f:
                    yaml.dump(config_dict, f, default_flow_style=False, allow_unicode=True)
            elif config_file.suffix.lower() == '.json':
                with open(config_file, 'w', encoding='utf-8') as f:
                    json.dump(config_dict, f, indent=2, ensure_ascii=False)
            else:
                raise ValueError(f"Unsupported config file format: {config_file.suffix}")
            
            logger.info(f"Configuration saved to: {self.config_path}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to save config: {e}")
            return False
    
    def _build_config_dict(self) -> Dict[str, Any]:
        """构建配置字典"""
        return {
            'data_sources': [ds.dict() for ds in self.data_sources.values()],
            'strategies': {name: strategy.dict() for name, strategy in self.strategies.items()},
            'filter_processor': self.filter_config.dict(),
            'search': self.search_config.dict()
        }
    
    def get_data_source_config(self, name: str) -> Optional[DataSourceConfig]:
        """获取数据源配置"""
        return self.data_sources.get(name)
    
    def get_enabled_data_sources(self) -> Dict[str, DataSourceConfig]:
        """获取启用的数据源配置"""
        return {name: ds for name, ds in self.data_sources.items() if ds.enabled}
    
    def get_strategy_config(self, name: str) -> Optional[StrategyConfig]:
        """获取策略配置"""
        return self.strategies.get(name)
    
    def get_enabled_strategies(self) -> Dict[str, StrategyConfig]:
        """获取启用的策略配置"""
        return {name: strategy for name, strategy in self.strategies.items() if strategy.enabled}
    
    def add_data_source(self, config: Dict[str, Any]) -> bool:
        """添加数据源配置"""
        try:
            ds = DataSourceConfig(**config)
            self.data_sources[ds.name] = ds
            logger.info(f"Data source config added: {ds.name}")
            return True
        except ValidationError as e:
            logger.error(f"Invalid data source config: {e}")
            return False
    
    def remove_data_source(self, name: str) -> bool:
        """移除数据源配置"""
        if name in self.data_sources:
            del self.data_sources[name]
            logger.info(f"Data source config removed: {name}")
            return True
        else:
            logger.warning(f"Data source config not found: {name}")
            return False
    
    def update_data_source(self, name: str, config: Dict[str, Any]) -> bool:
        """更新数据源配置"""
        if name not in self.data_sources:
            logger.warning(f"Data source config not found: {name}")
            return False
        
        try:
            # 合并配置
            current_config = self.data_sources[name].dict()
            current_config.update(config)
            
            # 验证新配置
            ds = DataSourceConfig(**current_config)
            self.data_sources[name] = ds
            
            logger.info(f"Data source config updated: {name}")
            return True
            
        except ValidationError as e:
            logger.error(f"Invalid data source config update: {e}")
            return False
    
    def get_full_config(self) -> Dict[str, Any]:
        """获取完整配置"""
        return {
            'data_sources': [ds.dict() for ds in self.data_sources.values()],
            'strategies': {name: strategy.dict() for name, strategy in self.strategies.items()},
            'filter_processor': self.filter_config.dict(),
            'search': self.search_config.dict()
        }
    
    def validate_config(self) -> Dict[str, Any]:
        """验证配置"""
        validation_result = {
            'valid': True,
            'errors': [],
            'warnings': []
        }
        
        # 检查是否有启用的数据源
        enabled_data_sources = self.get_enabled_data_sources()
        if not enabled_data_sources:
            validation_result['warnings'].append("No enabled data sources found")
        
        # 检查是否有启用的策略
        enabled_strategies = self.get_enabled_strategies()
        if not enabled_strategies:
            validation_result['errors'].append("No enabled strategies found")
            validation_result['valid'] = False
        
        # 检查数据源配置
        for name, ds in self.data_sources.items():
            if ds.enabled:
                if ds.type == 'elasticsearch' and not ds.host:
                    validation_result['errors'].append(f"Elasticsearch data source {name} missing host")
                    validation_result['valid'] = False
                elif ds.type == 'relational_db' and not ds.database:
                    validation_result['errors'].append(f"Database data source {name} missing database name")
                    validation_result['valid'] = False
        
        return validation_result
