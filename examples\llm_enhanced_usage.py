"""
LLM增强搜索系统使用示例
"""
import asyncio
import os
import json
from typing import List, Dict, Any

# 设置环境变量（实际使用时应该从环境变量或配置文件读取）
os.environ['OPENAI_API_KEY'] = 'your-openai-api-key-here'

from src.llm.enhanced_search_manager import EnhancedSearchManager
from src.core.interfaces import SearchQuery, SearchFilter, SearchType
from src.config.config_manager import ConfigManager


async def demo_enhanced_search():
    """演示LLM增强搜索功能"""
    print("=== LLM增强搜索系统演示 ===\n")
    
    # 1. 初始化配置和搜索管理器
    config_manager = ConfigManager("config/search_config.yaml")
    config = config_manager.get_config()
    
    search_manager = EnhancedSearchManager(config)
    
    try:
        await search_manager.initialize()
        print("✅ 搜索管理器初始化成功\n")
        
        # 2. 添加示例文档（带LLM预处理）
        await demo_document_processing(search_manager)
        
        # 3. 演示增强搜索功能
        await demo_query_understanding(search_manager)
        
        # 4. 演示结果优化
        await demo_result_optimization(search_manager)
        
        # 5. 演示答案生成
        await demo_answer_generation(search_manager)
        
        # 6. 演示搜索建议
        await demo_search_suggestions(search_manager)
        
    except Exception as e:
        print(f"❌ 演示过程中出现错误: {e}")
    
    finally:
        await search_manager.cleanup()
        print("\n🔚 演示结束")


async def demo_document_processing(search_manager: EnhancedSearchManager):
    """演示文档预处理功能"""
    print("📄 文档预处理演示")
    print("-" * 50)
    
    # 准备示例文档
    sample_documents = [
        {
            'id': 'doc_1',
            'content': '''
            人工智能（Artificial Intelligence，AI）是计算机科学的一个分支，
            它企图了解智能的实质，并生产出一种新的能以人类智能相似的方式做出反应的智能机器。
            该领域的研究包括机器人、语言识别、图像识别、自然语言处理和专家系统等。
            
            机器学习是人工智能的一个重要分支，它使用算法来解析数据、从中学习，
            然后对真实世界中的事件做出决策和预测。机器学习算法不是依赖于预先编程的指令，
            而是使用大量的数据来"训练"，以便学习如何执行任务。
            
            深度学习是机器学习的一个子集，它模仿人脑神经网络的工作方式。
            深度学习在图像识别、语音识别、自然语言处理等领域取得了突破性进展。
            ''',
            'metadata': {
                'category': 'technology',
                'tags': ['AI', 'machine-learning', 'deep-learning'],
                'created_at': '2024-01-01T10:00:00'
            }
        },
        {
            'id': 'doc_2',
            'content': '''
            Python是一种高级编程语言，由Guido van Rossum于1989年发明。
            Python具有简洁明了的语法，使得程序员可以用更少的代码表达想法。
            
            Python在数据科学领域特别受欢迎，主要原因包括：
            1. 丰富的数据科学库，如NumPy、Pandas、Matplotlib等
            2. 强大的机器学习框架，如Scikit-learn、TensorFlow、PyTorch
            3. 活跃的社区支持和丰富的学习资源
            4. 跨平台兼容性和易于部署
            
            在Web开发方面，Python也有Django和Flask等优秀的框架。
            ''',
            'metadata': {
                'category': 'programming',
                'tags': ['python', 'data-science', 'web-development'],
                'created_at': '2024-01-02T10:00:00'
            }
        }
    ]
    
    # 使用LLM预处理文档
    success = await search_manager.add_documents_with_processing(sample_documents)
    
    if success:
        print("✅ 文档预处理和索引完成")
        print(f"   - 处理了 {len(sample_documents)} 个原始文档")
        print("   - 生成了智能分块、摘要、关键词和语义标签")
    else:
        print("❌ 文档预处理失败")
    
    print()


async def demo_query_understanding(search_manager: EnhancedSearchManager):
    """演示查询理解功能"""
    print("🔍 查询理解演示")
    print("-" * 50)
    
    test_queries = [
        "什么是机器学习？",
        "Python在数据科学中的优势",
        "深度学习和机器学习的区别",
        "如何学习人工智能"
    ]
    
    for query_text in test_queries:
        print(f"原始查询: {query_text}")
        
        # 分析查询
        query_analysis = await search_manager.query_processor.analyze_query(query_text)
        
        print(f"  意图: {query_analysis.intent.value}")
        print(f"  关键词: {', '.join(query_analysis.keywords)}")
        print(f"  扩展查询: {', '.join(query_analysis.expanded_queries[:2])}")
        print(f"  语言: {query_analysis.language}")
        print(f"  置信度: {query_analysis.confidence:.2f}")
        print()


async def demo_result_optimization(search_manager: EnhancedSearchManager):
    """演示结果优化功能"""
    print("⚡ 结果优化演示")
    print("-" * 50)
    
    query = SearchQuery(
        query="机器学习的应用",
        search_type=SearchType.HYBRID,
        limit=5
    )
    
    # 执行增强搜索
    search_response = await search_manager.enhanced_search(query)
    
    print(f"查询: {query.query}")
    print(f"找到结果: {search_response['total_results']} 个")
    
    if 'optimized_results' in search_response:
        print("\n优化后的结果:")
        for i, opt_result in enumerate(search_response['optimized_results'][:3]):
            print(f"  {i+1}. 相关性: {opt_result.relevance_score:.2f}, "
                  f"质量: {opt_result.quality_score:.2f}, "
                  f"最终分数: {opt_result.final_score:.2f}")
            print(f"     内容预览: {opt_result.original_result.content[:100]}...")
            print(f"     来源: {opt_result.original_result.source}")
            print()


async def demo_answer_generation(search_manager: EnhancedSearchManager):
    """演示答案生成功能"""
    print("💡 答案生成演示")
    print("-" * 50)
    
    query = SearchQuery(
        query="Python在数据科学中有什么优势？",
        search_type=SearchType.HYBRID,
        limit=3
    )
    
    # 执行增强搜索
    search_response = await search_manager.enhanced_search(query)
    
    print(f"查询: {query.query}")
    
    if search_response.get('generated_answer'):
        answer_data = search_response['generated_answer']
        print(f"\n生成的答案:")
        print(f"{answer_data.get('answer', '无法生成答案')}")
        
        if answer_data.get('key_points'):
            print(f"\n关键要点:")
            for i, point in enumerate(answer_data['key_points'], 1):
                print(f"  {i}. {point}")
    else:
        print("未能生成答案")
    
    print()


async def demo_search_suggestions(search_manager: EnhancedSearchManager):
    """演示搜索建议功能"""
    print("💭 搜索建议演示")
    print("-" * 50)
    
    partial_queries = ["机器", "Python编程", "深度学习"]
    
    for partial_query in partial_queries:
        print(f"部分查询: '{partial_query}'")
        
        suggestions = await search_manager.get_search_suggestions(partial_query)
        
        if suggestions:
            print("  建议:")
            for i, suggestion in enumerate(suggestions, 1):
                print(f"    {i}. {suggestion}")
        else:
            print("  无建议")
        print()


async def demo_analytics(search_manager: EnhancedSearchManager):
    """演示分析功能"""
    print("📊 系统分析演示")
    print("-" * 50)
    
    analytics = await search_manager.get_analytics()
    
    print("系统状态:")
    print(f"  总查询数: {analytics['total_queries']}")
    print(f"  缓存命中率: {analytics['cache_hit_rate']:.2%}")
    
    print("\n数据源状态:")
    for ds_status in analytics['data_sources_status']:
        status = "✅ 健康" if ds_status['healthy'] else "❌ 异常"
        print(f"  {ds_status['name']} ({ds_status['type']}): {status}")
    
    print()


def print_json_pretty(data: Dict[str, Any], title: str = ""):
    """美化打印JSON数据"""
    if title:
        print(f"\n{title}:")
        print("-" * len(title))
    
    print(json.dumps(data, ensure_ascii=False, indent=2))
    print()


if __name__ == "__main__":
    # 运行演示
    asyncio.run(demo_enhanced_search())
