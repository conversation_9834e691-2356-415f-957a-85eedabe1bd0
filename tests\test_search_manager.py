"""
搜索管理器测试
"""
import pytest
import asyncio
from unittest.mock import Mock, AsyncMock, patch
import sys
from pathlib import Path

# 添加项目根目录到路径
sys.path.append(str(Path(__file__).parent.parent))

from src.core.search_manager import SearchManager
from src.core.interfaces import SearchQuery, SearchFilter, SearchType, SearchResult
from src.config.config_manager import ConfigManager


class TestSearchManager:
    """搜索管理器测试类"""
    
    @pytest.fixture
    def config_manager(self):
        """配置管理器fixture"""
        config_manager = ConfigManager()
        config_manager._load_default_config()
        return config_manager
    
    @pytest.fixture
    def search_manager(self, config_manager):
        """搜索管理器fixture"""
        return SearchManager(config_manager.get_full_config())
    
    @pytest.fixture
    def sample_search_results(self):
        """示例搜索结果"""
        return [
            SearchResult(
                id="1",
                content="Python编程语言介绍",
                score=0.9,
                metadata={"category": "programming"},
                source="test_source",
                search_type=SearchType.KEYWORD
            ),
            SearchResult(
                id="2",
                content="机器学习算法详解",
                score=0.8,
                metadata={"category": "ai"},
                source="test_source",
                search_type=SearchType.SEMANTIC
            )
        ]
    
    @pytest.mark.asyncio
    async def test_initialization(self, search_manager):
        """测试初始化"""
        with patch.object(search_manager, '_initialize_data_sources') as mock_ds, \
             patch.object(search_manager, '_initialize_strategies') as mock_strategies:
            
            mock_ds.return_value = None
            mock_strategies.return_value = None
            
            result = await search_manager.initialize()
            
            assert result is True
            assert search_manager.initialized is True
            mock_ds.assert_called_once()
            mock_strategies.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_search_with_keyword_strategy(self, search_manager, sample_search_results):
        """测试关键词检索"""
        # Mock策略
        mock_strategy = AsyncMock()
        mock_strategy.search.return_value = sample_search_results
        search_manager.strategies[SearchType.KEYWORD] = mock_strategy
        search_manager.initialized = True
        search_manager.data_sources = [Mock()]
        
        query = SearchQuery(
            query="Python编程",
            search_type=SearchType.KEYWORD,
            limit=10
        )
        
        results = await search_manager.search(query)
        
        assert len(results) == 2
        assert results[0].content == "Python编程语言介绍"
        mock_strategy.search.assert_called_once_with(search_manager.data_sources, query)
    
    @pytest.mark.asyncio
    async def test_search_with_filters(self, search_manager, sample_search_results):
        """测试带筛选条件的检索"""
        mock_strategy = AsyncMock()
        mock_strategy.search.return_value = sample_search_results
        search_manager.strategies[SearchType.HYBRID] = mock_strategy
        search_manager.initialized = True
        search_manager.data_sources = [Mock()]
        
        filters = [
            SearchFilter(field="category", operator="eq", value="programming")
        ]
        
        query = SearchQuery(
            query="编程",
            search_type=SearchType.HYBRID,
            filters=filters,
            limit=5
        )
        
        results = await search_manager.search(query)
        
        assert len(results) == 2
        mock_strategy.search.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_search_not_initialized(self, search_manager):
        """测试未初始化时的搜索"""
        query = SearchQuery(query="test", search_type=SearchType.KEYWORD)
        
        with pytest.raises(RuntimeError, match="Search manager not initialized"):
            await search_manager.search(query)
    
    @pytest.mark.asyncio
    async def test_search_no_data_sources(self, search_manager):
        """测试无数据源时的搜索"""
        search_manager.initialized = True
        search_manager.data_sources = []
        
        query = SearchQuery(query="test", search_type=SearchType.KEYWORD)
        results = await search_manager.search(query)
        
        assert results == []
    
    @pytest.mark.asyncio
    async def test_search_strategy_not_found(self, search_manager):
        """测试策略不存在时的搜索"""
        search_manager.initialized = True
        search_manager.data_sources = [Mock()]
        search_manager.strategies = {}
        
        query = SearchQuery(query="test", search_type=SearchType.KEYWORD)
        results = await search_manager.search(query)
        
        assert results == []
    
    def test_deduplicate_results(self, search_manager, sample_search_results):
        """测试结果去重"""
        # 添加重复内容的结果
        duplicate_result = SearchResult(
            id="3",
            content="Python编程语言介绍",  # 与第一个结果内容相同
            score=0.7,
            metadata={"category": "programming"},
            source="another_source",
            search_type=SearchType.KEYWORD
        )
        
        results_with_duplicates = sample_search_results + [duplicate_result]
        deduplicated = search_manager._deduplicate_results(results_with_duplicates)
        
        # 应该保留分数更高的结果
        assert len(deduplicated) == 2
        assert deduplicated[0].score == 0.9  # 保留分数更高的
    
    @pytest.mark.asyncio
    async def test_health_check(self, search_manager):
        """测试健康检查"""
        # Mock数据源
        mock_ds1 = Mock()
        mock_ds1.name = "test_ds1"
        mock_ds1.source_type = "test_type"
        mock_ds1.health_check = AsyncMock(return_value=True)
        
        mock_ds2 = Mock()
        mock_ds2.name = "test_ds2"
        mock_ds2.source_type = "test_type"
        mock_ds2.health_check = AsyncMock(return_value=False)
        
        search_manager.data_sources = [mock_ds1, mock_ds2]
        search_manager.strategies = {SearchType.KEYWORD: Mock()}
        search_manager.initialized = True
        
        health_status = await search_manager.health_check()
        
        assert health_status['initialized'] is True
        assert health_status['overall_status'] == 'degraded'  # 因为有一个数据源不健康
        assert len(health_status['data_sources']) == 2
        assert health_status['data_sources']['test_ds1']['status'] == 'healthy'
        assert health_status['data_sources']['test_ds2']['status'] == 'unhealthy'
    
    @pytest.mark.asyncio
    async def test_add_data_source(self, search_manager):
        """测试动态添加数据源"""
        with patch('src.datasources.vector_source.VectorSource') as MockVectorSource:
            mock_instance = AsyncMock()
            mock_instance.connect.return_value = True
            mock_instance.name = "new_vector_source"
            MockVectorSource.return_value = mock_instance
            
            config = {
                'name': 'new_vector_source',
                'type': 'vector_db',
                'model_name': 'test-model'
            }
            
            result = await search_manager.add_data_source(config)
            
            assert result is True
            assert len(search_manager.data_sources) == 1
            assert search_manager.data_sources[0].name == "new_vector_source"
    
    @pytest.mark.asyncio
    async def test_remove_data_source(self, search_manager):
        """测试移除数据源"""
        # 添加一个mock数据源
        mock_ds = AsyncMock()
        mock_ds.name = "test_source"
        mock_ds.disconnect = AsyncMock()
        search_manager.data_sources = [mock_ds]
        
        result = await search_manager.remove_data_source("test_source")
        
        assert result is True
        assert len(search_manager.data_sources) == 0
        mock_ds.disconnect.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_remove_nonexistent_data_source(self, search_manager):
        """测试移除不存在的数据源"""
        result = await search_manager.remove_data_source("nonexistent")
        assert result is False
    
    @pytest.mark.asyncio
    async def test_cleanup(self, search_manager):
        """测试清理资源"""
        # 添加mock数据源
        mock_ds1 = AsyncMock()
        mock_ds1.disconnect = AsyncMock()
        mock_ds2 = AsyncMock()
        mock_ds2.disconnect = AsyncMock()
        
        search_manager.data_sources = [mock_ds1, mock_ds2]
        
        await search_manager.cleanup()
        
        mock_ds1.disconnect.assert_called_once()
        mock_ds2.disconnect.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_context_manager(self, search_manager):
        """测试上下文管理器"""
        with patch.object(search_manager, 'initialize') as mock_init, \
             patch.object(search_manager, 'cleanup') as mock_cleanup:
            
            mock_init.return_value = True
            
            async with search_manager.get_manager() as manager:
                assert manager is search_manager
            
            mock_init.assert_called_once()
            mock_cleanup.assert_called_once()


if __name__ == "__main__":
    pytest.main([__file__])
