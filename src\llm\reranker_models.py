"""
专业Reranker模型集成
支持多种reranker模型，包括BGE、ColBERT、Cross-Encoder等
"""
import logging
import asyncio
import numpy as np
from typing import List, Dict, Any, Optional, Tuple, Union
from abc import ABC, abstractmethod
from dataclasses import dataclass
import torch
from transformers import AutoTokenizer, AutoModel, AutoModelForSequenceClassification
from sentence_transformers import SentenceTransformer, CrossEncoder
import requests
import json

from ..core.interfaces import SearchResult

logger = logging.getLogger(__name__)


@dataclass
class RerankResult:
    """重排序结果"""
    original_result: SearchResult
    rerank_score: float
    original_rank: int
    new_rank: int
    confidence: float = 0.0
    explanation: str = ""


class BaseReranker(ABC):
    """Reranker基础抽象类"""
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.model_name = config.get('model_name', '')
        self.batch_size = config.get('batch_size', 32)
        self.max_length = config.get('max_length', 512)
        self.device = config.get('device', 'cpu')
        self.initialized = False
    
    @abstractmethod
    async def initialize(self):
        """初始化模型"""
        pass
    
    @abstractmethod
    async def rerank(self, query: str, results: List[SearchResult]) -> List[RerankResult]:
        """重排序结果"""
        pass
    
    @abstractmethod
    def get_model_info(self) -> Dict[str, Any]:
        """获取模型信息"""
        pass


class BGEReranker(BaseReranker):
    """BGE Reranker模型"""
    
    def __init__(self, config: Dict[str, Any]):
        super().__init__(config)
        self.model_name = config.get('model_name', 'BAAI/bge-reranker-large')
        self.model = None
        self.tokenizer = None
    
    async def initialize(self):
        """初始化BGE模型"""
        try:
            logger.info(f"Initializing BGE reranker: {self.model_name}")
            
            # 在线程池中加载模型以避免阻塞
            loop = asyncio.get_event_loop()
            self.tokenizer, self.model = await loop.run_in_executor(
                None, self._load_model
            )
            
            self.initialized = True
            logger.info("BGE reranker initialized successfully")
            
        except Exception as e:
            logger.error(f"Failed to initialize BGE reranker: {e}")
            raise
    
    def _load_model(self):
        """加载模型（在线程池中执行）"""
        tokenizer = AutoTokenizer.from_pretrained(self.model_name)
        model = AutoModelForSequenceClassification.from_pretrained(self.model_name)
        
        if torch.cuda.is_available() and self.device == 'cuda':
            model = model.cuda()
        
        model.eval()
        return tokenizer, model
    
    async def rerank(self, query: str, results: List[SearchResult]) -> List[RerankResult]:
        """使用BGE模型重排序"""
        if not self.initialized:
            await self.initialize()
        
        if not results:
            return []
        
        try:
            # 准备输入对
            pairs = [(query, result.content) for result in results]
            
            # 批量处理
            scores = await self._batch_score(pairs)
            
            # 创建重排序结果
            rerank_results = []
            for i, (result, score) in enumerate(zip(results, scores)):
                rerank_result = RerankResult(
                    original_result=result,
                    rerank_score=float(score),
                    original_rank=i,
                    new_rank=0,  # 将在排序后设置
                    confidence=abs(float(score)),
                    explanation=f"BGE reranker score: {score:.4f}"
                )
                rerank_results.append(rerank_result)
            
            # 按重排序分数排序
            rerank_results.sort(key=lambda x: x.rerank_score, reverse=True)
            
            # 更新新排名
            for i, result in enumerate(rerank_results):
                result.new_rank = i
            
            return rerank_results
            
        except Exception as e:
            logger.error(f"BGE reranking failed: {e}")
            # 返回原始排序
            return [
                RerankResult(
                    original_result=result,
                    rerank_score=result.score,
                    original_rank=i,
                    new_rank=i,
                    explanation="Reranking failed, using original score"
                ) for i, result in enumerate(results)
            ]
    
    async def _batch_score(self, pairs: List[Tuple[str, str]]) -> List[float]:
        """批量计算相关性分数"""
        all_scores = []
        
        for i in range(0, len(pairs), self.batch_size):
            batch_pairs = pairs[i:i + self.batch_size]
            batch_scores = await self._score_batch(batch_pairs)
            all_scores.extend(batch_scores)
        
        return all_scores
    
    async def _score_batch(self, pairs: List[Tuple[str, str]]) -> List[float]:
        """计算一个批次的分数"""
        loop = asyncio.get_event_loop()
        return await loop.run_in_executor(None, self._compute_scores, pairs)
    
    def _compute_scores(self, pairs: List[Tuple[str, str]]) -> List[float]:
        """计算分数（在线程池中执行）"""
        with torch.no_grad():
            # 编码输入
            inputs = self.tokenizer(
                pairs,
                padding=True,
                truncation=True,
                max_length=self.max_length,
                return_tensors='pt'
            )
            
            if torch.cuda.is_available() and self.device == 'cuda':
                inputs = {k: v.cuda() for k, v in inputs.items()}
            
            # 前向传播
            outputs = self.model(**inputs)
            scores = outputs.logits.squeeze(-1)
            
            # 应用sigmoid激活
            scores = torch.sigmoid(scores)
            
            return scores.cpu().numpy().tolist()
    
    def get_model_info(self) -> Dict[str, Any]:
        """获取模型信息"""
        return {
            'name': 'BGE Reranker',
            'model_name': self.model_name,
            'type': 'cross_encoder',
            'device': self.device,
            'max_length': self.max_length,
            'batch_size': self.batch_size
        }


class CrossEncoderReranker(BaseReranker):
    """Cross-Encoder Reranker模型"""
    
    def __init__(self, config: Dict[str, Any]):
        super().__init__(config)
        self.model_name = config.get('model_name', 'cross-encoder/ms-marco-MiniLM-L-6-v2')
        self.model = None
    
    async def initialize(self):
        """初始化Cross-Encoder模型"""
        try:
            logger.info(f"Initializing Cross-Encoder reranker: {self.model_name}")
            
            loop = asyncio.get_event_loop()
            self.model = await loop.run_in_executor(
                None, self._load_model
            )
            
            self.initialized = True
            logger.info("Cross-Encoder reranker initialized successfully")
            
        except Exception as e:
            logger.error(f"Failed to initialize Cross-Encoder reranker: {e}")
            raise
    
    def _load_model(self):
        """加载模型"""
        return CrossEncoder(self.model_name, device=self.device)
    
    async def rerank(self, query: str, results: List[SearchResult]) -> List[RerankResult]:
        """使用Cross-Encoder重排序"""
        if not self.initialized:
            await self.initialize()
        
        if not results:
            return []
        
        try:
            # 准备输入对
            pairs = [[query, result.content] for result in results]
            
            # 计算分数
            loop = asyncio.get_event_loop()
            scores = await loop.run_in_executor(
                None, self.model.predict, pairs
            )
            
            # 创建重排序结果
            rerank_results = []
            for i, (result, score) in enumerate(zip(results, scores)):
                rerank_result = RerankResult(
                    original_result=result,
                    rerank_score=float(score),
                    original_rank=i,
                    new_rank=0,
                    confidence=abs(float(score)),
                    explanation=f"Cross-Encoder score: {score:.4f}"
                )
                rerank_results.append(rerank_result)
            
            # 排序并更新排名
            rerank_results.sort(key=lambda x: x.rerank_score, reverse=True)
            for i, result in enumerate(rerank_results):
                result.new_rank = i
            
            return rerank_results
            
        except Exception as e:
            logger.error(f"Cross-Encoder reranking failed: {e}")
            return [
                RerankResult(
                    original_result=result,
                    rerank_score=result.score,
                    original_rank=i,
                    new_rank=i,
                    explanation="Reranking failed, using original score"
                ) for i, result in enumerate(results)
            ]
    
    def get_model_info(self) -> Dict[str, Any]:
        """获取模型信息"""
        return {
            'name': 'Cross-Encoder Reranker',
            'model_name': self.model_name,
            'type': 'cross_encoder',
            'device': self.device,
            'max_length': self.max_length,
            'batch_size': self.batch_size
        }


class ColBERTReranker(BaseReranker):
    """ColBERT Reranker模型"""

    def __init__(self, config: Dict[str, Any]):
        super().__init__(config)
        self.model_name = config.get('model_name', 'colbert-ir/colbertv2.0')
        self.model = None
        self.tokenizer = None

    async def initialize(self):
        """初始化ColBERT模型"""
        try:
            logger.info(f"Initializing ColBERT reranker: {self.model_name}")

            loop = asyncio.get_event_loop()
            self.tokenizer, self.model = await loop.run_in_executor(
                None, self._load_model
            )

            self.initialized = True
            logger.info("ColBERT reranker initialized successfully")

        except Exception as e:
            logger.error(f"Failed to initialize ColBERT reranker: {e}")
            raise

    def _load_model(self):
        """加载ColBERT模型"""
        tokenizer = AutoTokenizer.from_pretrained(self.model_name)
        model = AutoModel.from_pretrained(self.model_name)

        if torch.cuda.is_available() and self.device == 'cuda':
            model = model.cuda()

        model.eval()
        return tokenizer, model

    async def rerank(self, query: str, results: List[SearchResult]) -> List[RerankResult]:
        """使用ColBERT重排序"""
        if not self.initialized:
            await self.initialize()

        if not results:
            return []

        try:
            # 计算ColBERT分数
            scores = await self._compute_colbert_scores(query, results)

            # 创建重排序结果
            rerank_results = []
            for i, (result, score) in enumerate(zip(results, scores)):
                rerank_result = RerankResult(
                    original_result=result,
                    rerank_score=float(score),
                    original_rank=i,
                    new_rank=0,
                    confidence=abs(float(score)),
                    explanation=f"ColBERT score: {score:.4f}"
                )
                rerank_results.append(rerank_result)

            # 排序并更新排名
            rerank_results.sort(key=lambda x: x.rerank_score, reverse=True)
            for i, result in enumerate(rerank_results):
                result.new_rank = i

            return rerank_results

        except Exception as e:
            logger.error(f"ColBERT reranking failed: {e}")
            return [
                RerankResult(
                    original_result=result,
                    rerank_score=result.score,
                    original_rank=i,
                    new_rank=i,
                    explanation="Reranking failed, using original score"
                ) for i, result in enumerate(results)
            ]

    async def _compute_colbert_scores(self, query: str, results: List[SearchResult]) -> List[float]:
        """计算ColBERT分数"""
        loop = asyncio.get_event_loop()
        return await loop.run_in_executor(None, self._colbert_score, query, results)

    def _colbert_score(self, query: str, results: List[SearchResult]) -> List[float]:
        """ColBERT评分计算"""
        with torch.no_grad():
            # 编码查询
            query_inputs = self.tokenizer(
                query,
                padding=True,
                truncation=True,
                max_length=self.max_length,
                return_tensors='pt'
            )

            if torch.cuda.is_available() and self.device == 'cuda':
                query_inputs = {k: v.cuda() for k, v in query_inputs.items()}

            query_outputs = self.model(**query_inputs)
            query_embeddings = query_outputs.last_hidden_state

            scores = []
            for result in results:
                # 编码文档
                doc_inputs = self.tokenizer(
                    result.content,
                    padding=True,
                    truncation=True,
                    max_length=self.max_length,
                    return_tensors='pt'
                )

                if torch.cuda.is_available() and self.device == 'cuda':
                    doc_inputs = {k: v.cuda() for k, v in doc_inputs.items()}

                doc_outputs = self.model(**doc_inputs)
                doc_embeddings = doc_outputs.last_hidden_state

                # 计算最大相似度
                similarity_matrix = torch.matmul(query_embeddings, doc_embeddings.transpose(-2, -1))
                max_sim = torch.max(similarity_matrix, dim=-1)[0]
                score = torch.mean(max_sim).item()
                scores.append(score)

            return scores

    def get_model_info(self) -> Dict[str, Any]:
        """获取模型信息"""
        return {
            'name': 'ColBERT Reranker',
            'model_name': self.model_name,
            'type': 'late_interaction',
            'device': self.device,
            'max_length': self.max_length,
            'batch_size': self.batch_size
        }


class CohereReranker(BaseReranker):
    """Cohere API Reranker"""

    def __init__(self, config: Dict[str, Any]):
        super().__init__(config)
        self.api_key = config.get('api_key', '')
        self.model_name = config.get('model_name', 'rerank-english-v2.0')
        self.api_url = config.get('api_url', 'https://api.cohere.ai/v1/rerank')
        self.top_n = config.get('top_n', 100)

    async def initialize(self):
        """初始化Cohere API"""
        if not self.api_key:
            raise ValueError("Cohere API key is required")

        self.initialized = True
        logger.info("Cohere reranker initialized successfully")

    async def rerank(self, query: str, results: List[SearchResult]) -> List[RerankResult]:
        """使用Cohere API重排序"""
        if not self.initialized:
            await self.initialize()

        if not results:
            return []

        try:
            # 准备API请求
            documents = [result.content for result in results]

            payload = {
                'model': self.model_name,
                'query': query,
                'documents': documents,
                'top_n': min(self.top_n, len(documents))
            }

            headers = {
                'Authorization': f'Bearer {self.api_key}',
                'Content-Type': 'application/json'
            }

            # 发送API请求
            async with asyncio.timeout(30):  # 30秒超时
                response = await self._make_api_request(payload, headers)

            # 处理响应
            rerank_results = self._process_cohere_response(response, results)
            return rerank_results

        except Exception as e:
            logger.error(f"Cohere reranking failed: {e}")
            return [
                RerankResult(
                    original_result=result,
                    rerank_score=result.score,
                    original_rank=i,
                    new_rank=i,
                    explanation="Reranking failed, using original score"
                ) for i, result in enumerate(results)
            ]

    async def _make_api_request(self, payload: Dict, headers: Dict) -> Dict:
        """发送API请求"""
        loop = asyncio.get_event_loop()
        return await loop.run_in_executor(None, self._sync_api_request, payload, headers)

    def _sync_api_request(self, payload: Dict, headers: Dict) -> Dict:
        """同步API请求"""
        response = requests.post(self.api_url, json=payload, headers=headers)
        response.raise_for_status()
        return response.json()

    def _process_cohere_response(self, response: Dict, original_results: List[SearchResult]) -> List[RerankResult]:
        """处理Cohere API响应"""
        rerank_results = []

        # 创建索引映射
        result_map = {i: result for i, result in enumerate(original_results)}

        # 处理重排序结果
        for item in response.get('results', []):
            index = item['index']
            score = item['relevance_score']

            if index in result_map:
                rerank_result = RerankResult(
                    original_result=result_map[index],
                    rerank_score=score,
                    original_rank=index,
                    new_rank=len(rerank_results),
                    confidence=score,
                    explanation=f"Cohere rerank score: {score:.4f}"
                )
                rerank_results.append(rerank_result)

        # 添加未被重排序的结果
        ranked_indices = {item['index'] for item in response.get('results', [])}
        for i, result in enumerate(original_results):
            if i not in ranked_indices:
                rerank_result = RerankResult(
                    original_result=result,
                    rerank_score=result.score * 0.5,  # 降低未重排序结果的分数
                    original_rank=i,
                    new_rank=len(rerank_results),
                    explanation="Not reranked by Cohere"
                )
                rerank_results.append(rerank_result)

        return rerank_results

    def get_model_info(self) -> Dict[str, Any]:
        """获取模型信息"""
        return {
            'name': 'Cohere Reranker',
            'model_name': self.model_name,
            'type': 'api',
            'api_url': self.api_url,
            'top_n': self.top_n
        }
