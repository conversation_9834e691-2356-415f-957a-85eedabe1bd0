"""
Elasticsearch数据源实现
"""
import logging
from typing import List, Dict, Any
from elasticsearch import AsyncElasticsearch
from elasticsearch.exceptions import ConnectionError, NotFoundError

from ..core.interfaces import DataSource, SearchQuery, SearchResult, SearchFilter, SearchType

logger = logging.getLogger(__name__)


class ElasticsearchSource(DataSource):
    """Elasticsearch数据源"""
    
    def __init__(self, config: Dict[str, Any]):
        super().__init__(config)
        self.client = None
        self.index_name = config.get('index_name', 'documents')
        self.host = config.get('host', 'localhost')
        self.port = config.get('port', 9200)
        self.username = config.get('username')
        self.password = config.get('password')
        
    async def connect(self) -> bool:
        """连接Elasticsearch"""
        try:
            auth = None
            if self.username and self.password:
                auth = (self.username, self.password)
            
            self.client = AsyncElasticsearch(
                hosts=[{'host': self.host, 'port': self.port}],
                http_auth=auth,
                verify_certs=False
            )
            
            # 测试连接
            await self.client.ping()
            logger.info(f"Connected to Elasticsearch at {self.host}:{self.port}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to connect to Elasticsearch: {e}")
            return False
    
    async def disconnect(self) -> None:
        """断开连接"""
        if self.client:
            await self.client.close()
            logger.info("Disconnected from Elasticsearch")
    
    async def search(self, query: SearchQuery) -> List[SearchResult]:
        """执行检索"""
        if not self.client:
            raise ConnectionError("Not connected to Elasticsearch")
        
        try:
            es_query = self._build_es_query(query)
            
            response = await self.client.search(
                index=self.index_name,
                body=es_query,
                size=query.limit,
                from_=query.offset
            )
            
            results = []
            for hit in response['hits']['hits']:
                result = SearchResult(
                    id=hit['_id'],
                    content=hit['_source'].get('content', ''),
                    score=hit['_score'],
                    metadata=hit['_source'],
                    source=self.name,
                    search_type=query.search_type
                )
                results.append(result)
            
            return results
            
        except Exception as e:
            logger.error(f"Search failed: {e}")
            return []
    
    def _build_es_query(self, query: SearchQuery) -> Dict[str, Any]:
        """构建Elasticsearch查询"""
        es_query = {
            "query": {
                "bool": {
                    "must": [],
                    "filter": []
                }
            }
        }
        
        # 构建主查询
        if query.search_type == SearchType.KEYWORD:
            es_query["query"]["bool"]["must"].append({
                "multi_match": {
                    "query": query.query,
                    "fields": ["content", "title"],
                    "type": "best_fields"
                }
            })
        elif query.search_type == SearchType.SEMANTIC:
            # 语义检索需要向量字段支持
            es_query["query"]["bool"]["must"].append({
                "script_score": {
                    "query": {"match_all": {}},
                    "script": {
                        "source": "cosineSimilarity(params.query_vector, 'content_vector') + 1.0",
                        "params": {
                            "query_vector": []  # 需要实际的向量
                        }
                    }
                }
            })
        else:  # HYBRID
            es_query["query"]["bool"]["must"].append({
                "multi_match": {
                    "query": query.query,
                    "fields": ["content^2", "title^3"],
                    "type": "best_fields"
                }
            })
        
        # 应用筛选条件
        if query.filters:
            for filter_condition in query.filters:
                filter_query = self._build_filter_query(filter_condition)
                if filter_query:
                    es_query["query"]["bool"]["filter"].append(filter_query)
        
        # 设置最小分数
        if query.min_score > 0:
            es_query["min_score"] = query.min_score
        
        return es_query
    
    def _build_filter_query(self, filter_condition: SearchFilter) -> Dict[str, Any]:
        """构建筛选查询"""
        field = filter_condition.field
        operator = filter_condition.operator
        value = filter_condition.value
        
        if operator == "eq":
            return {"term": {field: value}}
        elif operator == "ne":
            return {"bool": {"must_not": {"term": {field: value}}}}
        elif operator == "gt":
            return {"range": {field: {"gt": value}}}
        elif operator == "lt":
            return {"range": {field: {"lt": value}}}
        elif operator == "gte":
            return {"range": {field: {"gte": value}}}
        elif operator == "lte":
            return {"range": {field: {"lte": value}}}
        elif operator == "in":
            return {"terms": {field: value}}
        elif operator == "not_in":
            return {"bool": {"must_not": {"terms": {field: value}}}}
        elif operator == "contains":
            return {"wildcard": {field: f"*{value}*"}}
        
        return None
    
    async def health_check(self) -> bool:
        """健康检查"""
        try:
            if not self.client:
                return False
            
            await self.client.ping()
            
            # 检查索引是否存在
            exists = await self.client.indices.exists(index=self.index_name)
            return exists
            
        except Exception as e:
            logger.error(f"Health check failed: {e}")
            return False
