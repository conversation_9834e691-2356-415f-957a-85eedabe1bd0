"""
召回率优化和Reranker模型演示
展示如何使用多阶段检索、召回率优化和专业reranker模型
"""
import asyncio
import logging
import time
from typing import List, Dict, Any
import yaml

# 设置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# 导入必要的模块
import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from src.core.search_manager import SearchManager
from src.core.interfaces import SearchQuery, SearchType
from src.llm.recall_optimizer import RecallOptimizer
from src.llm.rerank_pipeline import RerankPipeline
from src.llm.reranker_models import BGEReranker, CrossEncoderReranker
from src.evaluation.retrieval_metrics import RetrievalEvaluator, RelevanceJudgment


async def demo_basic_reranker():
    """演示基础reranker使用"""
    print("\n=== 基础Reranker演示 ===")
    
    # 配置BGE reranker
    bge_config = {
        'model_name': 'BAAI/bge-reranker-base',
        'batch_size': 16,
        'max_length': 512,
        'device': 'cpu'
    }
    
    # 初始化reranker
    reranker = BGEReranker(bge_config)
    await reranker.initialize()
    
    # 模拟搜索结果
    from src.core.interfaces import SearchResult
    
    mock_results = [
        SearchResult(
            id="doc1",
            content="机器学习是人工智能的一个重要分支，通过算法让计算机从数据中学习模式。",
            score=0.8,
            metadata={},
            source="mock",
            search_type=SearchType.SEMANTIC
        ),
        SearchResult(
            id="doc2", 
            content="深度学习是机器学习的子领域，使用神经网络进行复杂的模式识别。",
            score=0.7,
            metadata={},
            source="mock",
            search_type=SearchType.SEMANTIC
        ),
        SearchResult(
            id="doc3",
            content="自然语言处理技术可以帮助计算机理解和生成人类语言。",
            score=0.6,
            metadata={},
            source="mock", 
            search_type=SearchType.SEMANTIC
        )
    ]
    
    # 执行重排序
    query = "什么是机器学习？"
    rerank_results = await reranker.rerank(query, mock_results)
    
    print(f"查询: {query}")
    print("重排序结果:")
    for i, result in enumerate(rerank_results):
        print(f"{i+1}. [原排名: {result.original_rank+1}] "
              f"分数: {result.rerank_score:.4f} - "
              f"{result.original_result.content[:50]}...")


async def demo_multi_reranker_pipeline():
    """演示多reranker管道"""
    print("\n=== 多Reranker管道演示 ===")
    
    # 配置多reranker管道
    pipeline_config = {
        'rerankers': [
            {
                'type': 'bge',
                'model_name': 'BAAI/bge-reranker-base',
                'batch_size': 16,
                'device': 'cpu'
            },
            {
                'type': 'cross_encoder',
                'model_name': 'cross-encoder/ms-marco-TinyBERT-L-2-v2',
                'device': 'cpu'
            }
        ],
        'fusion_method': 'weighted_average',
        'weights': [0.6, 0.4],
        'top_k': 5,
        'enable_score_normalization': True
    }
    
    # 初始化管道
    pipeline = RerankPipeline(pipeline_config)
    await pipeline.initialize()
    
    # 模拟更多搜索结果
    mock_results = [
        SearchResult(
            id="doc1",
            content="机器学习算法可以分为监督学习、无监督学习和强化学习三大类。",
            score=0.85,
            metadata={},
            source="mock",
            search_type=SearchType.HYBRID
        ),
        SearchResult(
            id="doc2",
            content="监督学习需要标注数据来训练模型，常用于分类和回归任务。",
            score=0.75,
            metadata={},
            source="mock",
            search_type=SearchType.HYBRID
        ),
        SearchResult(
            id="doc3",
            content="无监督学习不需要标注数据，主要用于聚类和降维。",
            score=0.70,
            metadata={},
            source="mock",
            search_type=SearchType.HYBRID
        ),
        SearchResult(
            id="doc4",
            content="强化学习通过与环境交互来学习最优策略。",
            score=0.65,
            metadata={},
            source="mock",
            search_type=SearchType.HYBRID
        ),
        SearchResult(
            id="doc5",
            content="深度学习使用多层神经网络来学习复杂的特征表示。",
            score=0.60,
            metadata={},
            source="mock",
            search_type=SearchType.HYBRID
        )
    ]
    
    # 执行管道重排序
    query = "机器学习有哪些类型？"
    pipeline_results = await pipeline.rerank(query, mock_results)
    
    print(f"查询: {query}")
    print("管道重排序结果:")
    for i, result in enumerate(pipeline_results):
        print(f"{i+1}. [原排名: {result.original_rank+1}] "
              f"分数: {result.rerank_score:.4f} - "
              f"{result.original_result.content[:60]}...")
    
    # 显示管道信息
    pipeline_info = pipeline.get_pipeline_info()
    print(f"\n管道信息: {pipeline_info}")


async def demo_recall_optimization():
    """演示召回率优化"""
    print("\n=== 召回率优化演示 ===")
    
    # 配置召回率优化器
    optimizer_config = {
        'enable_query_expansion': True,
        'max_expanded_queries': 3,
        'expansion_weight': 0.8,
        'enable_multi_model': True,
        'model_weights': {
            'keyword': 1.0,
            'semantic': 1.0,
            'hybrid': 1.2
        },
        'candidate_expansion_factor': 2,
        'min_candidate_score': 0.1,
        'enable_negative_sampling': True,
        'query_processor': {
            'model': 'gpt-3.5-turbo',
            'temperature': 0.1
        }
    }
    
    # 初始化优化器
    optimizer = RecallOptimizer(optimizer_config)
    await optimizer.initialize()
    
    # 模拟搜索函数
    async def mock_search_function(query):
        """模拟搜索函数"""
        # 这里应该是实际的搜索逻辑
        base_results = [
            SearchResult(
                id=f"doc{i}",
                content=f"关于{query.query}的文档内容 {i}",
                score=0.8 - i * 0.1,
                metadata={'mock': True},
                source="mock_source",
                search_type=query.search_type
            ) for i in range(5)
        ]
        return base_results
    
    # 创建查询
    query = SearchQuery(
        query="人工智能的应用",
        search_type=SearchType.HYBRID,
        limit=10
    )
    
    # 执行召回率优化
    optimized_results = await optimizer.optimize_recall(
        query, 
        mock_search_function,
        target_recall=0.8
    )
    
    print(f"查询: {query.query}")
    print(f"优化后结果数量: {len(optimized_results)}")
    for i, result in enumerate(optimized_results[:5]):
        print(f"{i+1}. 分数: {result.score:.4f} - {result.content[:50]}...")


async def demo_evaluation_metrics():
    """演示评估指标"""
    print("\n=== 评估指标演示 ===")
    
    # 配置评估器
    evaluator_config = {
        'k_values': [1, 3, 5, 10],
        'relevance_threshold': 1
    }
    
    evaluator = RetrievalEvaluator(evaluator_config)
    
    # 添加相关性判断数据
    relevance_judgments = [
        RelevanceJudgment("query1", "doc1", 3),  # 完美相关
        RelevanceJudgment("query1", "doc2", 2),  # 高度相关
        RelevanceJudgment("query1", "doc3", 1),  # 相关
        RelevanceJudgment("query1", "doc4", 0),  # 不相关
        RelevanceJudgment("query1", "doc5", 1),  # 相关
    ]
    
    evaluator.add_relevance_judgments(relevance_judgments)
    
    # 模拟搜索结果
    search_results = [
        SearchResult("doc2", "高度相关文档", 0.9, {}, "test", SearchType.HYBRID),
        SearchResult("doc1", "完美相关文档", 0.8, {}, "test", SearchType.HYBRID),
        SearchResult("doc4", "不相关文档", 0.7, {}, "test", SearchType.HYBRID),
        SearchResult("doc3", "相关文档", 0.6, {}, "test", SearchType.HYBRID),
        SearchResult("doc5", "相关文档", 0.5, {}, "test", SearchType.HYBRID),
    ]
    
    # 评估结果
    metrics = evaluator.evaluate("query1", search_results, response_time=0.1)
    
    print("评估指标:")
    print(f"Precision@5: {metrics.precision_at_k[5]:.4f}")
    print(f"Recall@5: {metrics.recall_at_k[5]:.4f}")
    print(f"F1@5: {metrics.f1_at_k[5]:.4f}")
    print(f"NDCG@5: {metrics.ndcg_at_k[5]:.4f}")
    print(f"MAP: {metrics.map_score:.4f}")
    print(f"MRR: {metrics.mrr_score:.4f}")
    print(f"响应时间: {metrics.response_time:.4f}s")


async def demo_complete_pipeline():
    """演示完整的优化管道"""
    print("\n=== 完整优化管道演示 ===")
    
    # 这里展示如何将所有组件整合在一起
    print("1. 初始化搜索管理器...")
    print("2. 配置多阶段检索策略...")
    print("3. 设置召回率优化...")
    print("4. 配置reranker管道...")
    print("5. 执行搜索和优化...")
    print("6. 评估结果...")
    
    print("\n完整管道配置示例:")
    complete_config = {
        'strategies': {
            'multi_stage': {
                'recall_limit': 50,
                'recall_strategies': ['keyword', 'semantic'],
                'enable_query_expansion': True,
                'reranker_type': 'bge',
                'reranker_config': {
                    'model_name': 'BAAI/bge-reranker-large',
                    'batch_size': 32,
                    'device': 'cpu'
                },
                'final_limit': 10,
                'fusion_method': 'weighted',
                'recall_weight': 0.3,
                'rerank_weight': 0.7
            }
        },
        'recall_optimizer': {
            'enable_query_expansion': True,
            'enable_multi_model': True,
            'enable_negative_sampling': True
        },
        'rerank_pipeline': {
            'single_reranker': {
                'type': 'bge',
                'model_name': 'BAAI/bge-reranker-large'
            }
        }
    }
    
    print(yaml.dump(complete_config, default_flow_style=False, allow_unicode=True))


async def main():
    """主演示函数"""
    print("🚀 召回率优化和Reranker模型演示")
    print("=" * 50)
    
    try:
        # 运行各个演示
        await demo_basic_reranker()
        await demo_multi_reranker_pipeline()
        await demo_recall_optimization()
        await demo_evaluation_metrics()
        await demo_complete_pipeline()
        
        print("\n✅ 所有演示完成！")
        
    except Exception as e:
        logger.error(f"演示过程中出现错误: {e}")
        print(f"\n❌ 演示失败: {e}")


if __name__ == "__main__":
    # 运行演示
    asyncio.run(main())
