"""
关键词检索策略实现
"""
import logging
import asyncio
from typing import List, Dict, Any
from collections import defaultdict

from ..core.interfaces import SearchStrategy, DataSource, SearchQuery, SearchResult, SearchType

logger = logging.getLogger(__name__)


class KeywordSearchStrategy(SearchStrategy):
    """关键词检索策略"""
    
    def __init__(self, config: Dict[str, Any]):
        super().__init__(config)
        self.boost_weights = config.get('boost_weights', {})
        self.merge_method = config.get('merge_method', 'score_weighted')
        
    async def search(self, data_sources: List[DataSource], query: SearchQuery) -> List[SearchResult]:
        """执行关键词检索"""
        # 确保使用关键词检索类型
        keyword_query = SearchQuery(
            query=query.query,
            search_type=SearchType.KEYWORD,
            filters=query.filters,
            limit=query.limit * 2,  # 获取更多结果用于合并
            offset=query.offset,
            boost_fields=query.boost_fields,
            min_score=query.min_score
        )
        
        # 并行搜索所有数据源
        tasks = []
        for data_source in data_sources:
            if await data_source.health_check():
                tasks.append(self._search_single_source(data_source, keyword_query))
            else:
                logger.warning(f"Data source {data_source.name} failed health check, skipping")
        
        if not tasks:
            return []
        
        # 等待所有搜索完成
        results_list = await asyncio.gather(*tasks, return_exceptions=True)
        
        # 过滤异常结果
        valid_results = []
        for i, result in enumerate(results_list):
            if isinstance(result, Exception):
                logger.error(f"Search failed for data source: {result}")
            else:
                valid_results.append(result)
        
        # 合并结果
        merged_results = self.merge_results(valid_results)
        
        # 返回指定数量的结果
        return merged_results[:query.limit]
    
    async def _search_single_source(self, data_source: DataSource, query: SearchQuery) -> List[SearchResult]:
        """搜索单个数据源"""
        try:
            results = await data_source.search(query)
            
            # 应用数据源权重
            source_weight = self.boost_weights.get(data_source.name, 1.0)
            for result in results:
                result.score *= source_weight
            
            return results
            
        except Exception as e:
            logger.error(f"Search failed for {data_source.name}: {e}")
            return []
    
    def merge_results(self, results_list: List[List[SearchResult]]) -> List[SearchResult]:
        """合并多个数据源的检索结果"""
        if not results_list:
            return []
        
        if self.merge_method == 'simple_concat':
            return self._simple_concat_merge(results_list)
        elif self.merge_method == 'score_weighted':
            return self._score_weighted_merge(results_list)
        elif self.merge_method == 'round_robin':
            return self._round_robin_merge(results_list)
        else:
            return self._score_weighted_merge(results_list)
    
    def _simple_concat_merge(self, results_list: List[List[SearchResult]]) -> List[SearchResult]:
        """简单拼接合并"""
        all_results = []
        for results in results_list:
            all_results.extend(results)
        
        # 按分数排序
        all_results.sort(key=lambda x: x.score, reverse=True)
        return all_results
    
    def _score_weighted_merge(self, results_list: List[List[SearchResult]]) -> List[SearchResult]:
        """基于分数的加权合并"""
        # 收集所有结果并去重
        result_map = {}
        
        for results in results_list:
            for result in results:
                key = f"{result.id}_{result.source}"
                if key in result_map:
                    # 如果同一文档在多个数据源中出现，取最高分数
                    if result.score > result_map[key].score:
                        result_map[key] = result
                else:
                    result_map[key] = result
        
        # 转换为列表并排序
        merged_results = list(result_map.values())
        merged_results.sort(key=lambda x: x.score, reverse=True)
        
        return merged_results
    
    def _round_robin_merge(self, results_list: List[List[SearchResult]]) -> List[SearchResult]:
        """轮询合并"""
        merged_results = []
        max_len = max(len(results) for results in results_list) if results_list else 0
        
        for i in range(max_len):
            for results in results_list:
                if i < len(results):
                    # 检查是否已经添加过相同的结果
                    result = results[i]
                    key = f"{result.id}_{result.source}"
                    
                    # 简单去重检查
                    if not any(f"{r.id}_{r.source}" == key for r in merged_results):
                        merged_results.append(result)
        
        return merged_results
