"""
混合检索策略实现
"""
import logging
import asyncio
from typing import List, Dict, Any
import numpy as np

from ..core.interfaces import SearchStrategy, DataSource, SearchQuery, SearchResult, SearchType
from .keyword_strategy import KeywordSearchStrategy
from .semantic_strategy import SemanticSearchStrategy

logger = logging.getLogger(__name__)


class HybridSearchStrategy(SearchStrategy):
    """混合检索策略"""
    
    def __init__(self, config: Dict[str, Any]):
        super().__init__(config)
        self.keyword_weight = config.get('keyword_weight', 0.4)
        self.semantic_weight = config.get('semantic_weight', 0.6)
        self.fusion_method = config.get('fusion_method', 'rrf')  # rrf, linear, max
        self.rrf_k = config.get('rrf_k', 60)  # RRF参数
        
        # 初始化子策略
        self.keyword_strategy = KeywordSearchStrategy(config.get('keyword_config', {}))
        self.semantic_strategy = SemanticSearchStrategy(config.get('semantic_config', {}))
        
    async def search(self, data_sources: List[DataSource], query: SearchQuery) -> List[SearchResult]:
        """执行混合检索"""
        # 并行执行关键词和语义检索
        keyword_task = self.keyword_strategy.search(data_sources, query)
        semantic_task = self.semantic_strategy.search(data_sources, query)
        
        try:
            keyword_results, semantic_results = await asyncio.gather(
                keyword_task, semantic_task, return_exceptions=True
            )
            
            # 处理异常
            if isinstance(keyword_results, Exception):
                logger.error(f"Keyword search failed: {keyword_results}")
                keyword_results = []
            
            if isinstance(semantic_results, Exception):
                logger.error(f"Semantic search failed: {semantic_results}")
                semantic_results = []
            
            # 融合结果
            fused_results = self._fuse_results(keyword_results, semantic_results, query)
            
            return fused_results[:query.limit]
            
        except Exception as e:
            logger.error(f"Hybrid search failed: {e}")
            return []
    
    def _fuse_results(self, keyword_results: List[SearchResult], 
                     semantic_results: List[SearchResult], 
                     query: SearchQuery) -> List[SearchResult]:
        """融合关键词和语义检索结果"""
        
        if self.fusion_method == 'rrf':
            return self._reciprocal_rank_fusion(keyword_results, semantic_results)
        elif self.fusion_method == 'linear':
            return self._linear_fusion(keyword_results, semantic_results)
        elif self.fusion_method == 'max':
            return self._max_fusion(keyword_results, semantic_results)
        else:
            return self._reciprocal_rank_fusion(keyword_results, semantic_results)
    
    def _reciprocal_rank_fusion(self, keyword_results: List[SearchResult], 
                               semantic_results: List[SearchResult]) -> List[SearchResult]:
        """倒数排名融合（RRF）"""
        # 创建结果映射
        result_map = {}
        
        # 处理关键词结果
        for rank, result in enumerate(keyword_results, 1):
            key = f"{result.id}_{result.source}"
            rrf_score = 1.0 / (self.rrf_k + rank)
            
            if key in result_map:
                result_map[key]['rrf_score'] += rrf_score * self.keyword_weight
                result_map[key]['keyword_rank'] = rank
            else:
                result_map[key] = {
                    'result': result,
                    'rrf_score': rrf_score * self.keyword_weight,
                    'keyword_rank': rank,
                    'semantic_rank': None
                }
        
        # 处理语义结果
        for rank, result in enumerate(semantic_results, 1):
            key = f"{result.id}_{result.source}"
            rrf_score = 1.0 / (self.rrf_k + rank)
            
            if key in result_map:
                result_map[key]['rrf_score'] += rrf_score * self.semantic_weight
                result_map[key]['semantic_rank'] = rank
            else:
                result_map[key] = {
                    'result': result,
                    'rrf_score': rrf_score * self.semantic_weight,
                    'keyword_rank': None,
                    'semantic_rank': rank
                }
        
        # 创建融合结果
        fused_results = []
        for key, data in result_map.items():
            result = data['result']
            
            # 创建新的结果对象
            fused_result = SearchResult(
                id=result.id,
                content=result.content,
                score=data['rrf_score'],
                metadata={
                    **result.metadata,
                    'keyword_rank': data['keyword_rank'],
                    'semantic_rank': data['semantic_rank'],
                    'fusion_method': 'rrf'
                },
                source=result.source,
                search_type=SearchType.HYBRID
            )
            fused_results.append(fused_result)
        
        # 按RRF分数排序
        fused_results.sort(key=lambda x: x.score, reverse=True)
        return fused_results
    
    def _linear_fusion(self, keyword_results: List[SearchResult], 
                      semantic_results: List[SearchResult]) -> List[SearchResult]:
        """线性融合"""
        result_map = {}
        
        # 归一化分数
        keyword_scores = [r.score for r in keyword_results] if keyword_results else [0]
        semantic_scores = [r.score for r in semantic_results] if semantic_results else [0]
        
        keyword_max = max(keyword_scores) if keyword_scores else 1.0
        semantic_max = max(semantic_scores) if semantic_scores else 1.0
        
        # 处理关键词结果
        for result in keyword_results:
            key = f"{result.id}_{result.source}"
            normalized_score = result.score / keyword_max if keyword_max > 0 else 0
            
            result_map[key] = {
                'result': result,
                'keyword_score': normalized_score,
                'semantic_score': 0.0
            }
        
        # 处理语义结果
        for result in semantic_results:
            key = f"{result.id}_{result.source}"
            normalized_score = result.score / semantic_max if semantic_max > 0 else 0
            
            if key in result_map:
                result_map[key]['semantic_score'] = normalized_score
            else:
                result_map[key] = {
                    'result': result,
                    'keyword_score': 0.0,
                    'semantic_score': normalized_score
                }
        
        # 创建融合结果
        fused_results = []
        for key, data in result_map.items():
            result = data['result']
            
            # 线性组合分数
            combined_score = (data['keyword_score'] * self.keyword_weight + 
                            data['semantic_score'] * self.semantic_weight)
            
            fused_result = SearchResult(
                id=result.id,
                content=result.content,
                score=combined_score,
                metadata={
                    **result.metadata,
                    'keyword_score': data['keyword_score'],
                    'semantic_score': data['semantic_score'],
                    'fusion_method': 'linear'
                },
                source=result.source,
                search_type=SearchType.HYBRID
            )
            fused_results.append(fused_result)
        
        # 按组合分数排序
        fused_results.sort(key=lambda x: x.score, reverse=True)
        return fused_results
    
    def _max_fusion(self, keyword_results: List[SearchResult], 
                   semantic_results: List[SearchResult]) -> List[SearchResult]:
        """最大值融合"""
        result_map = {}
        
        # 收集所有结果
        all_results = keyword_results + semantic_results
        
        for result in all_results:
            key = f"{result.id}_{result.source}"
            
            if key in result_map:
                # 取最大分数
                if result.score > result_map[key].score:
                    result_map[key] = result
            else:
                result_map[key] = result
        
        # 创建融合结果
        fused_results = []
        for result in result_map.values():
            fused_result = SearchResult(
                id=result.id,
                content=result.content,
                score=result.score,
                metadata={**result.metadata, 'fusion_method': 'max'},
                source=result.source,
                search_type=SearchType.HYBRID
            )
            fused_results.append(fused_result)
        
        # 按分数排序
        fused_results.sort(key=lambda x: x.score, reverse=True)
        return fused_results
    
    def merge_results(self, results_list: List[List[SearchResult]]) -> List[SearchResult]:
        """合并结果（由子策略处理）"""
        # 这个方法在混合策略中不直接使用
        # 因为融合逻辑在_fuse_results中处理
        if not results_list:
            return []
        
        all_results = []
        for results in results_list:
            all_results.extend(results)
        
        all_results.sort(key=lambda x: x.score, reverse=True)
        return all_results
