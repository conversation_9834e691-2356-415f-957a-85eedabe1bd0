# 部署和配置指南

## 系统要求

### 硬件要求

**最低配置**：
- CPU: 4核心
- 内存: 8GB RAM
- 存储: 50GB 可用空间
- 网络: 稳定的互联网连接

**推荐配置**：
- CPU: 8核心或更多
- 内存: 16GB RAM 或更多
- 存储: 100GB SSD
- GPU: NVIDIA GPU (可选，用于向量计算加速)
- 网络: 高速互联网连接

### 软件要求

- Python 3.8+
- pip 或 conda 包管理器
- Git (用于代码管理)
- Docker (可选，用于容器化部署)

### 外部服务

- **LLM API**: OpenAI API 或其他兼容的LLM服务
- **Elasticsearch**: 7.x 或 8.x (可选)
- **PostgreSQL**: 12+ (可选)
- **Redis**: 6+ (可选，用于缓存)

## 安装步骤

### 1. 克隆代码库

```bash
git clone <repository-url>
cd search_chain
```

### 2. 创建虚拟环境

```bash
# 使用 venv
python -m venv venv
source venv/bin/activate  # Linux/Mac
# 或
venv\Scripts\activate     # Windows

# 使用 conda
conda create -n search_chain python=3.9
conda activate search_chain
```

### 3. 安装依赖

```bash
pip install -r requirements.txt
```

### 4. 环境变量配置

创建 `.env` 文件：

```bash
# LLM API 配置
OPENAI_API_KEY=your-openai-api-key-here
OPENAI_API_BASE=https://api.openai.com/v1  # 可选，自定义API端点

# 可选：其他LLM提供商
ANTHROPIC_API_KEY=your-anthropic-api-key
AZURE_OPENAI_API_KEY=your-azure-openai-key
AZURE_OPENAI_ENDPOINT=your-azure-endpoint

# 数据库配置
POSTGRES_HOST=localhost
POSTGRES_PORT=5432
POSTGRES_DB=search_db
POSTGRES_USER=postgres
POSTGRES_PASSWORD=your-password

# Elasticsearch 配置
ELASTICSEARCH_HOST=localhost
ELASTICSEARCH_PORT=9200
ELASTICSEARCH_USERNAME=elastic
ELASTICSEARCH_PASSWORD=your-password

# Redis 配置
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=your-password

# 系统配置
LOG_LEVEL=INFO
DEBUG=false
```

### 5. 配置文件设置

编辑 `config/search_config.yaml`：

```yaml
# 数据源配置
data_sources:
  - name: "elasticsearch_main"
    type: "elasticsearch"
    host: "${ELASTICSEARCH_HOST}"
    port: ${ELASTICSEARCH_PORT}
    index_name: "documents"
    username: "${ELASTICSEARCH_USERNAME}"
    password: "${ELASTICSEARCH_PASSWORD}"
    enabled: true

  - name: "vector_store"
    type: "vector_db"
    model_name: "all-MiniLM-L6-v2"
    enabled: true
    config:
      dimension: 384
      similarity_metric: "cosine"
      use_gpu: false  # 设置为 true 如果有GPU

# LLM 配置
llm:
  document_processor:
    model: "gpt-3.5-turbo"
    temperature: 0.1
    max_tokens: 1000
    chunk_size: 1000
    chunk_overlap: 200
    
  query_processor:
    model: "gpt-3.5-turbo"
    temperature: 0.1
    max_tokens: 1000
    
  result_optimizer:
    model: "gpt-3.5-turbo"
    temperature: 0.1
    max_tokens: 1500
    relevance_weight: 0.6
    quality_weight: 0.4
  
  # 功能开关
  enable_query_enhancement: true
  enable_result_optimization: true
  enable_answer_generation: true

# 反馈系统配置
feedback:
  db_path: "data/feedback.db"
  model: "gpt-3.5-turbo"
  min_feedback_count: 10
  learning_interval: 3600  # 1小时
```

### 6. 数据库初始化

如果使用 PostgreSQL：

```bash
# 创建数据库
createdb search_db

# 运行初始化脚本
python scripts/init_database.py
```

如果使用 Elasticsearch：

```bash
# 创建索引
python scripts/init_elasticsearch.py
```

### 7. 验证安装

```bash
python -c "
import asyncio
from src.llm.enhanced_search_manager import EnhancedSearchManager
from src.config.config_manager import ConfigManager

async def test():
    config = ConfigManager('config/search_config.yaml').get_config()
    manager = EnhancedSearchManager(config)
    await manager.initialize()
    print('✅ 系统初始化成功')
    await manager.cleanup()

asyncio.run(test())
"
```

## 部署方式

### 1. 开发环境部署

```bash
# 启动开发服务器
python run_example.py --example

# 或运行完整演示
python examples/complete_llm_demo.py
```

### 2. 生产环境部署

#### 使用 Gunicorn + FastAPI

创建 `app.py`：

```python
from fastapi import FastAPI, HTTPException
from pydantic import BaseModel
from typing import List, Optional
import asyncio

from src.llm.enhanced_search_manager import EnhancedSearchManager
from src.core.interfaces import SearchQuery, SearchType
from src.config.config_manager import ConfigManager

app = FastAPI(title="Enhanced Search API", version="1.0.0")

# 全局搜索管理器
search_manager = None

@app.on_event("startup")
async def startup_event():
    global search_manager
    config = ConfigManager("config/search_config.yaml").get_config()
    search_manager = EnhancedSearchManager(config)
    await search_manager.initialize()

@app.on_event("shutdown")
async def shutdown_event():
    if search_manager:
        await search_manager.cleanup()

class SearchRequest(BaseModel):
    query: str
    search_type: str = "hybrid"
    limit: int = 10
    min_score: float = 0.0

class SearchResponse(BaseModel):
    query: str
    results: List[dict]
    generated_answer: Optional[dict]
    total_results: int

@app.post("/search", response_model=SearchResponse)
async def search(request: SearchRequest):
    try:
        query = SearchQuery(
            query=request.query,
            search_type=SearchType(request.search_type),
            limit=request.limit,
            min_score=request.min_score
        )
        
        response = await search_manager.enhanced_search(query)
        
        return SearchResponse(
            query=response['query'],
            results=[result.__dict__ for result in response['results']],
            generated_answer=response.get('generated_answer'),
            total_results=response['total_results']
        )
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/health")
async def health_check():
    return {"status": "healthy", "version": "1.0.0"}
```

启动服务：

```bash
# 安装 FastAPI 和 Gunicorn
pip install fastapi uvicorn gunicorn

# 开发环境
uvicorn app:app --reload --host 0.0.0.0 --port 8000

# 生产环境
gunicorn app:app -w 4 -k uvicorn.workers.UvicornWorker --bind 0.0.0.0:8000
```

#### 使用 Docker 部署

创建 `Dockerfile`：

```dockerfile
FROM python:3.9-slim

WORKDIR /app

# 安装系统依赖
RUN apt-get update && apt-get install -y \
    gcc \
    g++ \
    && rm -rf /var/lib/apt/lists/*

# 复制依赖文件
COPY requirements.txt .
RUN pip install --no-cache-dir -r requirements.txt

# 复制应用代码
COPY . .

# 创建数据目录
RUN mkdir -p data logs

# 暴露端口
EXPOSE 8000

# 启动命令
CMD ["gunicorn", "app:app", "-w", "4", "-k", "uvicorn.workers.UvicornWorker", "--bind", "0.0.0.0:8000"]
```

创建 `docker-compose.yml`：

```yaml
version: '3.8'

services:
  search-api:
    build: .
    ports:
      - "8000:8000"
    environment:
      - OPENAI_API_KEY=${OPENAI_API_KEY}
      - POSTGRES_HOST=postgres
      - ELASTICSEARCH_HOST=elasticsearch
      - REDIS_HOST=redis
    depends_on:
      - postgres
      - elasticsearch
      - redis
    volumes:
      - ./data:/app/data
      - ./logs:/app/logs

  postgres:
    image: postgres:13
    environment:
      - POSTGRES_DB=search_db
      - POSTGRES_USER=postgres
      - POSTGRES_PASSWORD=password
    volumes:
      - postgres_data:/var/lib/postgresql/data
    ports:
      - "5432:5432"

  elasticsearch:
    image: elasticsearch:8.8.0
    environment:
      - discovery.type=single-node
      - xpack.security.enabled=false
    volumes:
      - elasticsearch_data:/usr/share/elasticsearch/data
    ports:
      - "9200:9200"

  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data

volumes:
  postgres_data:
  elasticsearch_data:
  redis_data:
```

部署：

```bash
# 构建和启动
docker-compose up -d

# 查看日志
docker-compose logs -f search-api

# 停止服务
docker-compose down
```

### 3. Kubernetes 部署

创建 `k8s/deployment.yaml`：

```yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: search-api
spec:
  replicas: 3
  selector:
    matchLabels:
      app: search-api
  template:
    metadata:
      labels:
        app: search-api
    spec:
      containers:
      - name: search-api
        image: your-registry/search-api:latest
        ports:
        - containerPort: 8000
        env:
        - name: OPENAI_API_KEY
          valueFrom:
            secretKeyRef:
              name: api-secrets
              key: openai-api-key
        resources:
          requests:
            memory: "1Gi"
            cpu: "500m"
          limits:
            memory: "2Gi"
            cpu: "1000m"
---
apiVersion: v1
kind: Service
metadata:
  name: search-api-service
spec:
  selector:
    app: search-api
  ports:
  - port: 80
    targetPort: 8000
  type: LoadBalancer
```

部署到 Kubernetes：

```bash
# 创建密钥
kubectl create secret generic api-secrets \
  --from-literal=openai-api-key=your-api-key

# 部署应用
kubectl apply -f k8s/deployment.yaml

# 查看状态
kubectl get pods
kubectl get services
```

## 性能优化

### 1. 缓存配置

使用 Redis 进行缓存：

```python
# 在配置中启用缓存
cache:
  enabled: true
  backend: "redis"
  redis_url: "redis://localhost:6379/0"
  ttl: 3600  # 1小时过期
```

### 2. 数据库优化

PostgreSQL 优化：

```sql
-- 创建索引
CREATE INDEX idx_documents_content ON documents USING gin(to_tsvector('english', content));
CREATE INDEX idx_documents_category ON documents(category);
CREATE INDEX idx_documents_created_at ON documents(created_at);

-- 配置参数
ALTER SYSTEM SET shared_buffers = '256MB';
ALTER SYSTEM SET effective_cache_size = '1GB';
ALTER SYSTEM SET work_mem = '4MB';
```

Elasticsearch 优化：

```json
{
  "settings": {
    "number_of_shards": 1,
    "number_of_replicas": 0,
    "refresh_interval": "30s",
    "index.max_result_window": 50000
  },
  "mappings": {
    "properties": {
      "content": {
        "type": "text",
        "analyzer": "standard"
      },
      "content_vector": {
        "type": "dense_vector",
        "dims": 384
      }
    }
  }
}
```

### 3. 应用优化

```python
# 连接池配置
database:
  pool_size: 20
  max_overflow: 30
  pool_timeout: 30

# 异步配置
async:
  max_workers: 10
  timeout: 30

# LLM 配置
llm:
  max_concurrent_requests: 5
  request_timeout: 30
  retry_attempts: 3
```

## 监控和日志

### 1. 日志配置

创建 `logging.yaml`：

```yaml
version: 1
disable_existing_loggers: false

formatters:
  standard:
    format: '%(asctime)s [%(levelname)s] %(name)s: %(message)s'
  detailed:
    format: '%(asctime)s [%(levelname)s] %(name)s:%(lineno)d: %(message)s'

handlers:
  console:
    class: logging.StreamHandler
    level: INFO
    formatter: standard
    stream: ext://sys.stdout

  file:
    class: logging.handlers.RotatingFileHandler
    level: DEBUG
    formatter: detailed
    filename: logs/search_system.log
    maxBytes: 10485760  # 10MB
    backupCount: 5

loggers:
  src:
    level: DEBUG
    handlers: [console, file]
    propagate: false

root:
  level: INFO
  handlers: [console]
```

### 2. 健康检查

```python
@app.get("/health/detailed")
async def detailed_health_check():
    health_status = {
        "status": "healthy",
        "timestamp": datetime.now().isoformat(),
        "components": {}
    }
    
    # 检查数据源
    for ds in search_manager.data_sources:
        try:
            is_healthy = await ds.health_check()
            health_status["components"][ds.name] = {
                "status": "healthy" if is_healthy else "unhealthy",
                "type": ds.source_type
            }
        except Exception as e:
            health_status["components"][ds.name] = {
                "status": "error",
                "error": str(e)
            }
    
    # 检查LLM连接
    try:
        test_response = await search_manager.query_processor.llm.ainvoke("test")
        health_status["components"]["llm"] = {"status": "healthy"}
    except Exception as e:
        health_status["components"]["llm"] = {
            "status": "error",
            "error": str(e)
        }
    
    return health_status
```

### 3. 指标收集

使用 Prometheus 进行监控：

```python
from prometheus_client import Counter, Histogram, Gauge

# 定义指标
search_requests_total = Counter('search_requests_total', 'Total search requests')
search_duration_seconds = Histogram('search_duration_seconds', 'Search duration')
active_connections = Gauge('active_connections', 'Active connections')

# 在搜索函数中记录指标
@search_requests_total.count_exceptions()
@search_duration_seconds.time()
async def enhanced_search(self, query):
    # 搜索逻辑
    pass
```

## 故障排除

### 常见问题

1. **API 密钥错误**
   ```
   错误: openai.error.AuthenticationError
   解决: 检查 OPENAI_API_KEY 环境变量
   ```

2. **内存不足**
   ```
   错误: OutOfMemoryError
   解决: 增加系统内存或减少批处理大小
   ```

3. **连接超时**
   ```
   错误: asyncio.TimeoutError
   解决: 增加超时时间或检查网络连接
   ```

### 调试模式

启用调试模式：

```bash
export DEBUG=true
export LOG_LEVEL=DEBUG
python examples/complete_llm_demo.py
```

这个部署指南涵盖了从开发到生产的完整部署流程，确保系统能够稳定运行。
