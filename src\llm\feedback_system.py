"""
实时反馈和学习系统
"""
import logging
import asyncio
import json
import time
from typing import List, Dict, Any, Optional, Tuple
from dataclasses import dataclass, asdict
from enum import Enum
from datetime import datetime, timedelta
import sqlite3
import aiosqlite

from langchain_openai import ChatOpenAI
from langchain.prompts import PromptTemplate

logger = logging.getLogger(__name__)


class FeedbackType(Enum):
    """反馈类型"""
    RELEVANCE = "relevance"  # 相关性反馈
    QUALITY = "quality"      # 质量反馈
    USEFULNESS = "usefulness"  # 有用性反馈
    ACCURACY = "accuracy"    # 准确性反馈


class FeedbackRating(Enum):
    """反馈评分"""
    VERY_BAD = 1
    BAD = 2
    NEUTRAL = 3
    GOOD = 4
    VERY_GOOD = 5


@dataclass
class UserFeedback:
    """用户反馈数据结构"""
    id: str
    query: str
    result_id: str
    feedback_type: FeedbackType
    rating: FeedbackRating
    comment: Optional[str] = None
    timestamp: Optional[datetime] = None
    user_id: Optional[str] = None
    session_id: Optional[str] = None
    metadata: Optional[Dict[str, Any]] = None


@dataclass
class LearningInsight:
    """学习洞察"""
    pattern: str
    confidence: float
    recommendation: str
    affected_queries: List[str]
    improvement_suggestion: str


class FeedbackSystem:
    """反馈和学习系统"""
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.db_path = config.get('db_path', 'feedback.db')
        self.llm = ChatOpenAI(
            model=config.get('model', 'gpt-3.5-turbo'),
            temperature=config.get('temperature', 0.1),
            max_tokens=config.get('max_tokens', 1000)
        )
        
        # 学习参数
        self.min_feedback_count = config.get('min_feedback_count', 10)
        self.learning_interval = config.get('learning_interval', 3600)  # 1小时
        self.last_learning_time = 0
        
        # 缓存
        self.feedback_cache = []
        self.insights_cache = []
        
        self._init_prompts()
    
    def _init_prompts(self):
        """初始化提示模板"""
        
        # 反馈分析提示
        self.feedback_analysis_prompt = PromptTemplate(
            input_variables=["feedback_data"],
            template="""分析以下用户反馈数据，识别模式和改进机会：

反馈数据：
{feedback_data}

请分析：
1. 用户满意度趋势
2. 常见问题和抱怨
3. 高质量结果的特征
4. 需要改进的查询类型
5. 具体的改进建议

返回JSON格式：
{{
    "patterns": [
        {{
            "pattern": "模式描述",
            "confidence": 0.85,
            "recommendation": "改进建议",
            "affected_queries": ["查询1", "查询2"],
            "improvement_suggestion": "具体改进方案"
        }}
    ],
    "overall_satisfaction": 0.75,
    "key_issues": ["问题1", "问题2"],
    "recommendations": ["建议1", "建议2"]
}}"""
        )
        
        # 查询优化建议提示
        self.query_optimization_prompt = PromptTemplate(
            input_variables=["query", "negative_feedback"],
            template="""基于用户的负面反馈，为以下查询提供优化建议：

原始查询：{query}

负面反馈：
{negative_feedback}

请提供：
1. 查询重写建议
2. 搜索策略调整
3. 结果过滤优化
4. 用户体验改进

返回JSON格式：
{{
    "rewritten_queries": ["优化查询1", "优化查询2"],
    "strategy_adjustments": ["调整1", "调整2"],
    "filter_suggestions": ["过滤建议1", "过滤建议2"],
    "ux_improvements": ["体验改进1", "体验改进2"]
}}"""
        )
    
    async def initialize(self):
        """初始化反馈系统"""
        try:
            await self._init_database()
            logger.info("Feedback system initialized successfully")
            return True
        except Exception as e:
            logger.error(f"Failed to initialize feedback system: {e}")
            return False
    
    async def _init_database(self):
        """初始化数据库"""
        async with aiosqlite.connect(self.db_path) as db:
            # 创建反馈表
            await db.execute('''
                CREATE TABLE IF NOT EXISTS feedback (
                    id TEXT PRIMARY KEY,
                    query TEXT NOT NULL,
                    result_id TEXT NOT NULL,
                    feedback_type TEXT NOT NULL,
                    rating INTEGER NOT NULL,
                    comment TEXT,
                    timestamp DATETIME DEFAULT CURRENT_TIMESTAMP,
                    user_id TEXT,
                    session_id TEXT,
                    metadata TEXT
                )
            ''')
            
            # 创建学习洞察表
            await db.execute('''
                CREATE TABLE IF NOT EXISTS insights (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    pattern TEXT NOT NULL,
                    confidence REAL NOT NULL,
                    recommendation TEXT NOT NULL,
                    affected_queries TEXT NOT NULL,
                    improvement_suggestion TEXT NOT NULL,
                    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                    applied BOOLEAN DEFAULT FALSE
                )
            ''')
            
            # 创建查询性能表
            await db.execute('''
                CREATE TABLE IF NOT EXISTS query_performance (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    query TEXT NOT NULL,
                    avg_rating REAL,
                    feedback_count INTEGER,
                    last_updated DATETIME DEFAULT CURRENT_TIMESTAMP
                )
            ''')
            
            await db.commit()
    
    async def collect_feedback(self, feedback: UserFeedback) -> bool:
        """收集用户反馈"""
        try:
            # 设置时间戳
            if not feedback.timestamp:
                feedback.timestamp = datetime.now()
            
            # 保存到数据库
            async with aiosqlite.connect(self.db_path) as db:
                await db.execute('''
                    INSERT INTO feedback 
                    (id, query, result_id, feedback_type, rating, comment, 
                     timestamp, user_id, session_id, metadata)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                ''', (
                    feedback.id,
                    feedback.query,
                    feedback.result_id,
                    feedback.feedback_type.value,
                    feedback.rating.value,
                    feedback.comment,
                    feedback.timestamp,
                    feedback.user_id,
                    feedback.session_id,
                    json.dumps(feedback.metadata) if feedback.metadata else None
                ))
                await db.commit()
            
            # 添加到缓存
            self.feedback_cache.append(feedback)
            
            # 更新查询性能统计
            await self._update_query_performance(feedback.query, feedback.rating.value)
            
            # 检查是否需要触发学习
            if len(self.feedback_cache) >= self.min_feedback_count:
                await self._trigger_learning()
            
            logger.info(f"Collected feedback for query: {feedback.query}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to collect feedback: {e}")
            return False
    
    async def _update_query_performance(self, query: str, rating: int):
        """更新查询性能统计"""
        try:
            async with aiosqlite.connect(self.db_path) as db:
                # 检查是否已存在
                cursor = await db.execute(
                    'SELECT avg_rating, feedback_count FROM query_performance WHERE query = ?',
                    (query,)
                )
                row = await cursor.fetchone()
                
                if row:
                    # 更新现有记录
                    old_avg, old_count = row
                    new_count = old_count + 1
                    new_avg = (old_avg * old_count + rating) / new_count
                    
                    await db.execute('''
                        UPDATE query_performance 
                        SET avg_rating = ?, feedback_count = ?, last_updated = CURRENT_TIMESTAMP
                        WHERE query = ?
                    ''', (new_avg, new_count, query))
                else:
                    # 插入新记录
                    await db.execute('''
                        INSERT INTO query_performance (query, avg_rating, feedback_count)
                        VALUES (?, ?, ?)
                    ''', (query, rating, 1))
                
                await db.commit()
                
        except Exception as e:
            logger.error(f"Failed to update query performance: {e}")
    
    async def _trigger_learning(self):
        """触发学习过程"""
        current_time = time.time()
        if current_time - self.last_learning_time < self.learning_interval:
            return
        
        try:
            # 分析反馈数据
            insights = await self._analyze_feedback()
            
            # 保存洞察
            if insights:
                await self._save_insights(insights)
                self.insights_cache.extend(insights)
            
            # 清空缓存
            self.feedback_cache.clear()
            self.last_learning_time = current_time
            
            logger.info(f"Learning triggered, generated {len(insights)} insights")
            
        except Exception as e:
            logger.error(f"Learning process failed: {e}")
    
    async def _analyze_feedback(self) -> List[LearningInsight]:
        """分析反馈数据"""
        if not self.feedback_cache:
            return []
        
        try:
            # 准备反馈数据
            feedback_data = []
            for feedback in self.feedback_cache:
                data = {
                    'query': feedback.query,
                    'rating': feedback.rating.value,
                    'type': feedback.feedback_type.value,
                    'comment': feedback.comment
                }
                feedback_data.append(data)
            
            feedback_json = json.dumps(feedback_data, ensure_ascii=False, indent=2)
            
            # 使用LLM分析
            prompt = self.feedback_analysis_prompt.format(feedback_data=feedback_json)
            response = await self.llm.ainvoke(prompt)
            
            # 解析响应
            try:
                analysis_result = json.loads(response.content.strip())
                patterns = analysis_result.get('patterns', [])
                
                insights = []
                for pattern_data in patterns:
                    insight = LearningInsight(
                        pattern=pattern_data.get('pattern', ''),
                        confidence=pattern_data.get('confidence', 0.5),
                        recommendation=pattern_data.get('recommendation', ''),
                        affected_queries=pattern_data.get('affected_queries', []),
                        improvement_suggestion=pattern_data.get('improvement_suggestion', '')
                    )
                    insights.append(insight)
                
                return insights
                
            except json.JSONDecodeError:
                logger.warning("Failed to parse LLM analysis response")
                return []
                
        except Exception as e:
            logger.error(f"Feedback analysis failed: {e}")
            return []
    
    async def _save_insights(self, insights: List[LearningInsight]):
        """保存学习洞察"""
        try:
            async with aiosqlite.connect(self.db_path) as db:
                for insight in insights:
                    await db.execute('''
                        INSERT INTO insights 
                        (pattern, confidence, recommendation, affected_queries, improvement_suggestion)
                        VALUES (?, ?, ?, ?, ?)
                    ''', (
                        insight.pattern,
                        insight.confidence,
                        insight.recommendation,
                        json.dumps(insight.affected_queries),
                        insight.improvement_suggestion
                    ))
                await db.commit()
                
        except Exception as e:
            logger.error(f"Failed to save insights: {e}")
    
    async def get_query_optimization_suggestions(self, query: str) -> Dict[str, Any]:
        """获取查询优化建议"""
        try:
            # 获取该查询的负面反馈
            negative_feedback = await self._get_negative_feedback(query)
            
            if not negative_feedback:
                return {'suggestions': [], 'confidence': 0.0}
            
            # 准备负面反馈数据
            feedback_text = '\n'.join([
                f"评分: {fb['rating']}, 评论: {fb['comment'] or '无'}"
                for fb in negative_feedback
            ])
            
            # 使用LLM生成优化建议
            prompt = self.query_optimization_prompt.format(
                query=query,
                negative_feedback=feedback_text
            )
            response = await self.llm.ainvoke(prompt)
            
            # 解析响应
            try:
                suggestions = json.loads(response.content.strip())
                return {
                    'suggestions': suggestions,
                    'confidence': min(len(negative_feedback) / 5.0, 1.0)  # 基于反馈数量的置信度
                }
            except json.JSONDecodeError:
                return {'suggestions': [], 'confidence': 0.0}
                
        except Exception as e:
            logger.error(f"Failed to get optimization suggestions: {e}")
            return {'suggestions': [], 'confidence': 0.0}
    
    async def _get_negative_feedback(self, query: str) -> List[Dict[str, Any]]:
        """获取负面反馈"""
        try:
            async with aiosqlite.connect(self.db_path) as db:
                cursor = await db.execute('''
                    SELECT rating, comment, feedback_type 
                    FROM feedback 
                    WHERE query = ? AND rating <= 2
                    ORDER BY timestamp DESC
                    LIMIT 10
                ''', (query,))
                
                rows = await cursor.fetchall()
                return [
                    {
                        'rating': row[0],
                        'comment': row[1],
                        'type': row[2]
                    }
                    for row in rows
                ]
                
        except Exception as e:
            logger.error(f"Failed to get negative feedback: {e}")
            return []
    
    async def get_performance_metrics(self) -> Dict[str, Any]:
        """获取性能指标"""
        try:
            async with aiosqlite.connect(self.db_path) as db:
                # 总体统计
                cursor = await db.execute('SELECT COUNT(*), AVG(rating) FROM feedback')
                total_feedback, avg_rating = await cursor.fetchone()
                
                # 按类型统计
                cursor = await db.execute('''
                    SELECT feedback_type, COUNT(*), AVG(rating) 
                    FROM feedback 
                    GROUP BY feedback_type
                ''')
                type_stats = await cursor.fetchall()
                
                # 最差查询
                cursor = await db.execute('''
                    SELECT query, avg_rating, feedback_count 
                    FROM query_performance 
                    WHERE feedback_count >= 3
                    ORDER BY avg_rating ASC 
                    LIMIT 5
                ''')
                worst_queries = await cursor.fetchall()
                
                # 最佳查询
                cursor = await db.execute('''
                    SELECT query, avg_rating, feedback_count 
                    FROM query_performance 
                    WHERE feedback_count >= 3
                    ORDER BY avg_rating DESC 
                    LIMIT 5
                ''')
                best_queries = await cursor.fetchall()
                
                return {
                    'total_feedback': total_feedback or 0,
                    'average_rating': avg_rating or 0.0,
                    'type_statistics': [
                        {
                            'type': row[0],
                            'count': row[1],
                            'avg_rating': row[2]
                        }
                        for row in type_stats
                    ],
                    'worst_queries': [
                        {
                            'query': row[0],
                            'avg_rating': row[1],
                            'feedback_count': row[2]
                        }
                        for row in worst_queries
                    ],
                    'best_queries': [
                        {
                            'query': row[0],
                            'avg_rating': row[1],
                            'feedback_count': row[2]
                        }
                        for row in best_queries
                    ],
                    'insights_count': len(self.insights_cache)
                }
                
        except Exception as e:
            logger.error(f"Failed to get performance metrics: {e}")
            return {}
    
    async def get_recent_insights(self, limit: int = 10) -> List[Dict[str, Any]]:
        """获取最近的学习洞察"""
        try:
            async with aiosqlite.connect(self.db_path) as db:
                cursor = await db.execute('''
                    SELECT pattern, confidence, recommendation, 
                           affected_queries, improvement_suggestion, created_at
                    FROM insights 
                    ORDER BY created_at DESC 
                    LIMIT ?
                ''', (limit,))
                
                rows = await cursor.fetchall()
                return [
                    {
                        'pattern': row[0],
                        'confidence': row[1],
                        'recommendation': row[2],
                        'affected_queries': json.loads(row[3]),
                        'improvement_suggestion': row[4],
                        'created_at': row[5]
                    }
                    for row in rows
                ]
                
        except Exception as e:
            logger.error(f"Failed to get recent insights: {e}")
            return []
