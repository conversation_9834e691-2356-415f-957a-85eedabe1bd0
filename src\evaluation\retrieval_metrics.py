"""
检索系统性能评估指标
包括召回率、精确率、NDCG等指标的计算
"""
import logging
import numpy as np
from typing import List, Dict, Any, Optional, Tuple, Set
from dataclasses import dataclass
from collections import defaultdict
import math

from ..core.interfaces import SearchResult

logger = logging.getLogger(__name__)


@dataclass
class RelevanceJudgment:
    """相关性判断"""
    query_id: str
    doc_id: str
    relevance: int  # 0: 不相关, 1: 相关, 2: 高度相关, 3: 完美相关


@dataclass
class RetrievalMetrics:
    """检索指标"""
    # 基础指标
    precision_at_k: Dict[int, float]
    recall_at_k: Dict[int, float]
    f1_at_k: Dict[int, float]
    
    # 排序指标
    ndcg_at_k: Dict[int, float]
    map_score: float  # Mean Average Precision
    mrr_score: float  # Mean Reciprocal Rank
    
    # 多样性指标
    diversity_at_k: Dict[int, float]
    coverage: float
    
    # 效率指标
    response_time: float
    throughput: float


class RetrievalEvaluator:
    """检索系统评估器"""
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.k_values = config.get('k_values', [1, 3, 5, 10, 20])
        self.relevance_threshold = config.get('relevance_threshold', 1)
        
        # 相关性判断数据
        self.relevance_judgments: Dict[str, Dict[str, int]] = {}
        
    def add_relevance_judgments(self, judgments: List[RelevanceJudgment]):
        """添加相关性判断数据"""
        for judgment in judgments:
            if judgment.query_id not in self.relevance_judgments:
                self.relevance_judgments[judgment.query_id] = {}
            
            self.relevance_judgments[judgment.query_id][judgment.doc_id] = judgment.relevance
    
    def evaluate(self, query_id: str, results: List[SearchResult], 
                response_time: float = 0.0) -> RetrievalMetrics:
        """评估检索结果"""
        try:
            # 获取相关性判断
            relevance_map = self.relevance_judgments.get(query_id, {})
            
            if not relevance_map:
                logger.warning(f"No relevance judgments found for query: {query_id}")
                return self._create_empty_metrics(response_time)
            
            # 计算基础指标
            precision_at_k = self._calculate_precision_at_k(results, relevance_map)
            recall_at_k = self._calculate_recall_at_k(results, relevance_map)
            f1_at_k = self._calculate_f1_at_k(precision_at_k, recall_at_k)
            
            # 计算排序指标
            ndcg_at_k = self._calculate_ndcg_at_k(results, relevance_map)
            map_score = self._calculate_map(results, relevance_map)
            mrr_score = self._calculate_mrr(results, relevance_map)
            
            # 计算多样性指标
            diversity_at_k = self._calculate_diversity_at_k(results)
            coverage = self._calculate_coverage(results, relevance_map)
            
            # 计算效率指标
            throughput = len(results) / response_time if response_time > 0 else 0.0
            
            return RetrievalMetrics(
                precision_at_k=precision_at_k,
                recall_at_k=recall_at_k,
                f1_at_k=f1_at_k,
                ndcg_at_k=ndcg_at_k,
                map_score=map_score,
                mrr_score=mrr_score,
                diversity_at_k=diversity_at_k,
                coverage=coverage,
                response_time=response_time,
                throughput=throughput
            )
            
        except Exception as e:
            logger.error(f"Evaluation failed for query {query_id}: {e}")
            return self._create_empty_metrics(response_time)
    
    def _calculate_precision_at_k(self, results: List[SearchResult], 
                                 relevance_map: Dict[str, int]) -> Dict[int, float]:
        """计算Precision@K"""
        precision_at_k = {}
        
        for k in self.k_values:
            if k > len(results):
                precision_at_k[k] = 0.0
                continue
            
            relevant_count = 0
            for i in range(k):
                doc_id = results[i].id
                if relevance_map.get(doc_id, 0) >= self.relevance_threshold:
                    relevant_count += 1
            
            precision_at_k[k] = relevant_count / k
        
        return precision_at_k
    
    def _calculate_recall_at_k(self, results: List[SearchResult], 
                              relevance_map: Dict[str, int]) -> Dict[int, float]:
        """计算Recall@K"""
        recall_at_k = {}
        
        # 计算总相关文档数
        total_relevant = sum(1 for rel in relevance_map.values() 
                           if rel >= self.relevance_threshold)
        
        if total_relevant == 0:
            return {k: 0.0 for k in self.k_values}
        
        for k in self.k_values:
            if k > len(results):
                k = len(results)
            
            relevant_retrieved = 0
            for i in range(k):
                doc_id = results[i].id
                if relevance_map.get(doc_id, 0) >= self.relevance_threshold:
                    relevant_retrieved += 1
            
            recall_at_k[k] = relevant_retrieved / total_relevant
        
        return recall_at_k
    
    def _calculate_f1_at_k(self, precision_at_k: Dict[int, float], 
                          recall_at_k: Dict[int, float]) -> Dict[int, float]:
        """计算F1@K"""
        f1_at_k = {}
        
        for k in self.k_values:
            p = precision_at_k.get(k, 0.0)
            r = recall_at_k.get(k, 0.0)
            
            if p + r > 0:
                f1_at_k[k] = 2 * (p * r) / (p + r)
            else:
                f1_at_k[k] = 0.0
        
        return f1_at_k
    
    def _calculate_ndcg_at_k(self, results: List[SearchResult], 
                            relevance_map: Dict[str, int]) -> Dict[int, float]:
        """计算NDCG@K"""
        ndcg_at_k = {}
        
        for k in self.k_values:
            if k > len(results):
                k = len(results)
            
            # 计算DCG@K
            dcg = 0.0
            for i in range(k):
                doc_id = results[i].id
                relevance = relevance_map.get(doc_id, 0)
                if relevance > 0:
                    dcg += (2 ** relevance - 1) / math.log2(i + 2)
            
            # 计算IDCG@K
            ideal_relevances = sorted(relevance_map.values(), reverse=True)[:k]
            idcg = 0.0
            for i, relevance in enumerate(ideal_relevances):
                if relevance > 0:
                    idcg += (2 ** relevance - 1) / math.log2(i + 2)
            
            # 计算NDCG@K
            if idcg > 0:
                ndcg_at_k[k] = dcg / idcg
            else:
                ndcg_at_k[k] = 0.0
        
        return ndcg_at_k
    
    def _calculate_map(self, results: List[SearchResult], 
                      relevance_map: Dict[str, int]) -> float:
        """计算Mean Average Precision"""
        relevant_count = 0
        precision_sum = 0.0
        
        for i, result in enumerate(results):
            doc_id = result.id
            if relevance_map.get(doc_id, 0) >= self.relevance_threshold:
                relevant_count += 1
                precision_at_i = relevant_count / (i + 1)
                precision_sum += precision_at_i
        
        total_relevant = sum(1 for rel in relevance_map.values() 
                           if rel >= self.relevance_threshold)
        
        if total_relevant > 0:
            return precision_sum / total_relevant
        else:
            return 0.0
    
    def _calculate_mrr(self, results: List[SearchResult], 
                      relevance_map: Dict[str, int]) -> float:
        """计算Mean Reciprocal Rank"""
        for i, result in enumerate(results):
            doc_id = result.id
            if relevance_map.get(doc_id, 0) >= self.relevance_threshold:
                return 1.0 / (i + 1)
        
        return 0.0
    
    def _calculate_diversity_at_k(self, results: List[SearchResult]) -> Dict[int, float]:
        """计算多样性@K"""
        diversity_at_k = {}
        
        for k in self.k_values:
            if k > len(results):
                k = len(results)
            
            if k <= 1:
                diversity_at_k[k] = 0.0
                continue
            
            # 计算内容多样性（基于Jaccard距离）
            total_distance = 0.0
            pair_count = 0
            
            for i in range(k):
                for j in range(i + 1, k):
                    distance = self._jaccard_distance(
                        results[i].content, 
                        results[j].content
                    )
                    total_distance += distance
                    pair_count += 1
            
            if pair_count > 0:
                diversity_at_k[k] = total_distance / pair_count
            else:
                diversity_at_k[k] = 0.0
        
        return diversity_at_k
    
    def _calculate_coverage(self, results: List[SearchResult], 
                           relevance_map: Dict[str, int]) -> float:
        """计算覆盖率"""
        total_relevant = sum(1 for rel in relevance_map.values() 
                           if rel >= self.relevance_threshold)
        
        if total_relevant == 0:
            return 0.0
        
        covered_relevant = 0
        for result in results:
            if relevance_map.get(result.id, 0) >= self.relevance_threshold:
                covered_relevant += 1
        
        return covered_relevant / total_relevant
    
    def _jaccard_distance(self, text1: str, text2: str) -> float:
        """计算Jaccard距离"""
        try:
            words1 = set(text1.lower().split())
            words2 = set(text2.lower().split())
            
            intersection = len(words1 & words2)
            union = len(words1 | words2)
            
            if union == 0:
                return 0.0
            
            jaccard_similarity = intersection / union
            return 1.0 - jaccard_similarity
            
        except Exception:
            return 0.0
    
    def _create_empty_metrics(self, response_time: float) -> RetrievalMetrics:
        """创建空的指标对象"""
        return RetrievalMetrics(
            precision_at_k={k: 0.0 for k in self.k_values},
            recall_at_k={k: 0.0 for k in self.k_values},
            f1_at_k={k: 0.0 for k in self.k_values},
            ndcg_at_k={k: 0.0 for k in self.k_values},
            map_score=0.0,
            mrr_score=0.0,
            diversity_at_k={k: 0.0 for k in self.k_values},
            coverage=0.0,
            response_time=response_time,
            throughput=0.0
        )
    
    def batch_evaluate(self, query_results: Dict[str, Tuple[List[SearchResult], float]]) -> Dict[str, RetrievalMetrics]:
        """批量评估多个查询"""
        results = {}
        
        for query_id, (search_results, response_time) in query_results.items():
            metrics = self.evaluate(query_id, search_results, response_time)
            results[query_id] = metrics
        
        return results
    
    def aggregate_metrics(self, metrics_list: List[RetrievalMetrics]) -> RetrievalMetrics:
        """聚合多个查询的指标"""
        if not metrics_list:
            return self._create_empty_metrics(0.0)
        
        # 聚合各项指标
        agg_precision_at_k = {}
        agg_recall_at_k = {}
        agg_f1_at_k = {}
        agg_ndcg_at_k = {}
        agg_diversity_at_k = {}
        
        for k in self.k_values:
            agg_precision_at_k[k] = np.mean([m.precision_at_k.get(k, 0.0) for m in metrics_list])
            agg_recall_at_k[k] = np.mean([m.recall_at_k.get(k, 0.0) for m in metrics_list])
            agg_f1_at_k[k] = np.mean([m.f1_at_k.get(k, 0.0) for m in metrics_list])
            agg_ndcg_at_k[k] = np.mean([m.ndcg_at_k.get(k, 0.0) for m in metrics_list])
            agg_diversity_at_k[k] = np.mean([m.diversity_at_k.get(k, 0.0) for m in metrics_list])
        
        return RetrievalMetrics(
            precision_at_k=agg_precision_at_k,
            recall_at_k=agg_recall_at_k,
            f1_at_k=agg_f1_at_k,
            ndcg_at_k=agg_ndcg_at_k,
            map_score=np.mean([m.map_score for m in metrics_list]),
            mrr_score=np.mean([m.mrr_score for m in metrics_list]),
            diversity_at_k=agg_diversity_at_k,
            coverage=np.mean([m.coverage for m in metrics_list]),
            response_time=np.mean([m.response_time for m in metrics_list]),
            throughput=np.mean([m.throughput for m in metrics_list])
        )
