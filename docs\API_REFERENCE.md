# API 参考文档

## 概述

本文档详细描述了大模型增强检索系统的所有API接口，包括请求参数、响应格式和使用示例。

## 核心API接口

### 1. EnhancedSearchManager

#### 1.1 enhanced_search()

**功能**：执行增强搜索，返回优化后的结果和生成的答案

```python
async def enhanced_search(self, query: SearchQuery) -> Dict[str, Any]
```

**参数**：
- `query` (SearchQuery): 搜索查询对象

**SearchQuery 结构**：
```python
@dataclass
class SearchQuery:
    query: str                              # 查询文本
    search_type: SearchType = SearchType.HYBRID  # 搜索类型
    filters: List[SearchFilter] = None      # 筛选条件
    limit: int = 10                         # 结果数量限制
    offset: int = 0                         # 结果偏移量
    boost_fields: Dict[str, float] = None   # 字段权重
    min_score: float = 0.0                  # 最小分数阈值
```

**返回值**：
```python
{
    'query': str,                    # 原始查询
    'query_analysis': QueryAnalysis, # 查询分析结果
    'results': List[SearchResult],   # 搜索结果列表
    'optimized_results': List[OptimizedResult],  # 优化后结果
    'generated_answer': Dict[str, Any],          # 生成的答案
    'total_results': int,            # 结果总数
    'search_time': float            # 搜索耗时
}
```

**使用示例**：
```python
from src.llm.enhanced_search_manager import EnhancedSearchManager
from src.core.interfaces import SearchQuery, SearchType

search_manager = EnhancedSearchManager(config)
await search_manager.initialize()

query = SearchQuery(
    query="人工智能的应用领域有哪些？",
    search_type=SearchType.HYBRID,
    limit=5,
    min_score=0.3
)

response = await search_manager.enhanced_search(query)

print(f"找到 {response['total_results']} 个结果")
print(f"生成答案: {response['generated_answer']['answer']}")
```

#### 1.2 add_documents_with_processing()

**功能**：添加文档并进行LLM预处理

```python
async def add_documents_with_processing(self, documents: List[Dict[str, Any]]) -> bool
```

**参数**：
- `documents` (List[Dict]): 文档列表

**文档格式**：
```python
{
    'id': str,                    # 文档ID
    'content': str,               # 文档内容
    'metadata': Dict[str, Any]    # 元数据
}
```

**返回值**：
- `bool`: 处理是否成功

**使用示例**：
```python
documents = [
    {
        'id': 'doc_1',
        'content': '人工智能是计算机科学的一个分支...',
        'metadata': {
            'category': 'technology',
            'author': 'AI Expert',
            'created_at': '2024-01-01T10:00:00'
        }
    }
]

success = await search_manager.add_documents_with_processing(documents)
if success:
    print("文档处理和索引完成")
```

#### 1.3 get_search_suggestions()

**功能**：基于部分查询获取搜索建议

```python
async def get_search_suggestions(self, partial_query: str) -> List[str]
```

**参数**：
- `partial_query` (str): 部分查询文本

**返回值**：
- `List[str]`: 搜索建议列表

**使用示例**：
```python
suggestions = await search_manager.get_search_suggestions("机器学习")
print("搜索建议:", suggestions)
# 输出: ['机器学习算法', '机器学习应用', '机器学习入门', ...]
```

#### 1.4 explain_search_results()

**功能**：解释搜索结果的相关性

```python
async def explain_search_results(self, query: str, results: List[SearchResult]) -> str
```

**参数**：
- `query` (str): 查询文本
- `results` (List[SearchResult]): 搜索结果

**返回值**：
- `str`: 结果解释文本

#### 1.5 get_analytics()

**功能**：获取系统分析数据

```python
async def get_analytics(self) -> Dict[str, Any]
```

**返回值**：
```python
{
    'total_queries': int,           # 总查询数
    'cache_hit_rate': float,        # 缓存命中率
    'average_results_per_query': float,  # 平均结果数
    'most_common_intents': List[str],    # 常见意图
    'data_sources_status': List[Dict]    # 数据源状态
}
```

### 2. LLMDocumentProcessor

#### 2.1 process_document()

**功能**：处理单个文档

```python
async def process_document(self, document: Dict[str, Any]) -> List[DocumentChunk]
```

**参数**：
- `document` (Dict): 文档数据

**返回值**：
- `List[DocumentChunk]`: 处理后的文档块列表

**DocumentChunk 结构**：
```python
@dataclass
class DocumentChunk:
    content: str                    # 块内容
    metadata: Dict[str, Any]        # 元数据
    chunk_id: str                   # 块ID
    parent_doc_id: str              # 父文档ID
    chunk_index: int                # 块索引
    summary: Optional[str] = None   # 摘要
    keywords: Optional[List[str]] = None      # 关键词
    semantic_tags: Optional[List[str]] = None # 语义标签
```

#### 2.2 batch_process_documents()

**功能**：批量处理文档

```python
async def batch_process_documents(self, documents: List[Dict[str, Any]], 
                                batch_size: int = 5) -> List[DocumentChunk]
```

**参数**：
- `documents` (List[Dict]): 文档列表
- `batch_size` (int): 批处理大小

**使用示例**：
```python
from src.llm.document_processor import LLMDocumentProcessor

processor = LLMDocumentProcessor({
    'model': 'gpt-3.5-turbo',
    'chunk_size': 1000,
    'chunk_overlap': 200
})

chunks = await processor.batch_process_documents(documents, batch_size=3)
print(f"生成了 {len(chunks)} 个文档块")
```

### 3. LLMQueryProcessor

#### 3.1 analyze_query()

**功能**：分析查询意图和特征

```python
async def analyze_query(self, query: str) -> QueryAnalysis
```

**参数**：
- `query` (str): 查询文本

**返回值**：
- `QueryAnalysis`: 查询分析结果

**QueryAnalysis 结构**：
```python
@dataclass
class QueryAnalysis:
    original_query: str             # 原始查询
    intent: QueryIntent             # 查询意图
    expanded_queries: List[str]     # 扩展查询
    keywords: List[str]             # 关键词
    entities: List[str]             # 实体
    synonyms: Dict[str, List[str]]  # 同义词
    language: str                   # 语言
    confidence: float               # 置信度
    suggested_filters: List[Dict[str, Any]]  # 建议筛选条件
```

**QueryIntent 枚举**：
```python
class QueryIntent(Enum):
    FACTUAL = "factual"         # 事实查询
    CONCEPTUAL = "conceptual"   # 概念查询
    PROCEDURAL = "procedural"   # 过程查询
    COMPARATIVE = "comparative" # 比较查询
    ANALYTICAL = "analytical"   # 分析查询
```

**使用示例**：
```python
from src.llm.query_processor import LLMQueryProcessor

processor = LLMQueryProcessor({'model': 'gpt-3.5-turbo'})
analysis = await processor.analyze_query("什么是机器学习？")

print(f"查询意图: {analysis.intent.value}")
print(f"关键词: {analysis.keywords}")
print(f"扩展查询: {analysis.expanded_queries}")
```

#### 3.2 process_multi_language_query()

**功能**：处理多语言查询

```python
async def process_multi_language_query(self, query: str, target_language: str = 'zh') -> str
```

**参数**：
- `query` (str): 查询文本
- `target_language` (str): 目标语言

**返回值**：
- `str`: 翻译后的查询

### 4. LLMResultOptimizer

#### 4.1 optimize_results()

**功能**：优化搜索结果

```python
async def optimize_results(self, results: List[SearchResult], 
                         query: SearchQuery) -> List[OptimizedResult]
```

**参数**：
- `results` (List[SearchResult]): 原始搜索结果
- `query` (SearchQuery): 查询对象

**返回值**：
- `List[OptimizedResult]`: 优化后的结果

**OptimizedResult 结构**：
```python
@dataclass
class OptimizedResult:
    original_result: SearchResult   # 原始结果
    relevance_score: float          # 相关性分数
    quality_score: float            # 质量分数
    final_score: float              # 最终分数
    explanation: str                # 评估解释
    generated_answer: Optional[str] = None    # 生成答案
    key_points: Optional[List[str]] = None    # 关键要点
```

#### 4.2 generate_answer()

**功能**：基于搜索结果生成答案

```python
async def generate_answer(self, results: List[OptimizedResult], 
                        query: str) -> Optional[Dict[str, Any]]
```

**参数**：
- `results` (List[OptimizedResult]): 优化后的结果
- `query` (str): 查询文本

**返回值**：
```python
{
    'answer': str,              # 生成的答案
    'key_points': List[str]     # 关键要点
}
```

### 5. FeedbackSystem

#### 5.1 collect_feedback()

**功能**：收集用户反馈

```python
async def collect_feedback(self, feedback: UserFeedback) -> bool
```

**参数**：
- `feedback` (UserFeedback): 用户反馈对象

**UserFeedback 结构**：
```python
@dataclass
class UserFeedback:
    id: str                         # 反馈ID
    query: str                      # 查询文本
    result_id: str                  # 结果ID
    feedback_type: FeedbackType     # 反馈类型
    rating: FeedbackRating          # 评分
    comment: Optional[str] = None   # 评论
    timestamp: Optional[datetime] = None  # 时间戳
    user_id: Optional[str] = None   # 用户ID
    session_id: Optional[str] = None # 会话ID
```

**FeedbackType 枚举**：
```python
class FeedbackType(Enum):
    RELEVANCE = "relevance"     # 相关性
    QUALITY = "quality"         # 质量
    USEFULNESS = "usefulness"   # 有用性
    ACCURACY = "accuracy"       # 准确性
```

**FeedbackRating 枚举**：
```python
class FeedbackRating(Enum):
    VERY_BAD = 1    # 很差
    BAD = 2         # 差
    NEUTRAL = 3     # 一般
    GOOD = 4        # 好
    VERY_GOOD = 5   # 很好
```

**使用示例**：
```python
from src.llm.feedback_system import FeedbackSystem, UserFeedback, FeedbackType, FeedbackRating

feedback_system = FeedbackSystem(config)
await feedback_system.initialize()

feedback = UserFeedback(
    id="feedback_1",
    query="机器学习算法",
    result_id="result_1",
    feedback_type=FeedbackType.RELEVANCE,
    rating=FeedbackRating.GOOD,
    comment="结果很相关，找到了需要的信息"
)

success = await feedback_system.collect_feedback(feedback)
```

#### 5.2 get_performance_metrics()

**功能**：获取性能指标

```python
async def get_performance_metrics(self) -> Dict[str, Any]
```

**返回值**：
```python
{
    'total_feedback': int,          # 总反馈数
    'average_rating': float,        # 平均评分
    'type_statistics': List[Dict],  # 按类型统计
    'worst_queries': List[Dict],    # 最差查询
    'best_queries': List[Dict],     # 最佳查询
    'insights_count': int           # 洞察数量
}
```

#### 5.3 get_query_optimization_suggestions()

**功能**：获取查询优化建议

```python
async def get_query_optimization_suggestions(self, query: str) -> Dict[str, Any]
```

**参数**：
- `query` (str): 查询文本

**返回值**：
```python
{
    'suggestions': Dict[str, List[str]],  # 优化建议
    'confidence': float                   # 置信度
}
```

### 6. AdvancedVectorStore

#### 6.1 add_documents()

**功能**：添加文档到向量存储

```python
async def add_documents(self, documents: List[Dict[str, Any]]) -> bool
```

#### 6.2 search()

**功能**：执行向量搜索

```python
async def search(self, query: SearchQuery) -> List[SearchResult]
```

#### 6.3 get_statistics()

**功能**：获取向量存储统计信息

```python
def get_statistics(self) -> Dict[str, Any]
```

**返回值**：
```python
{
    'total_documents': int,     # 文档总数
    'index_size': int,          # 索引大小
    'clusters': int,            # 集群数量
    'dimension': int,           # 向量维度
    'models': Dict[str, str]    # 使用的模型
}
```

## 错误处理

所有API都包含完善的错误处理机制：

```python
try:
    response = await search_manager.enhanced_search(query)
except ConnectionError as e:
    print(f"连接错误: {e}")
except ValueError as e:
    print(f"参数错误: {e}")
except Exception as e:
    print(f"未知错误: {e}")
```

## 配置参数

详细的配置参数说明请参考 `config/search_config.yaml` 文件和配置文档。
