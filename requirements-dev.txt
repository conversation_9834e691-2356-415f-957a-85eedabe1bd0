# 开发和测试依赖

# 代码格式化和检查
black>=23.0.0
isort>=5.12.0
flake8>=6.0.0
mypy>=1.0.0

# 测试框架
pytest>=7.0.0
pytest-asyncio>=0.21.0
pytest-cov>=4.0.0
pytest-mock>=3.10.0

# 文档生成
sphinx>=6.0.0
sphinx-rtd-theme>=1.2.0
myst-parser>=1.0.0

# 性能分析
memory-profiler>=0.60.0
line-profiler>=4.0.0
py-spy>=0.3.14

# 开发工具
pre-commit>=3.0.0
jupyter>=1.0.0
ipython>=8.0.0

# API开发
fastapi>=0.100.0
uvicorn>=0.22.0
gunicorn>=21.0.0

# 监控和日志
prometheus-client>=0.16.0
structlog>=23.0.0
