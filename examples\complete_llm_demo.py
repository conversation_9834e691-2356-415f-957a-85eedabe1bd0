"""
完整的LLM增强搜索系统演示
"""
import asyncio
import os
import json
import uuid
from datetime import datetime
from typing import List, Dict, Any

# 设置环境变量
os.environ['OPENAI_API_KEY'] = 'your-openai-api-key-here'

from src.llm.enhanced_search_manager import EnhancedSearchManager
from src.llm.feedback_system import FeedbackSystem, UserFeedback, FeedbackType, FeedbackRating
from src.core.interfaces import SearchQuery, SearchFilter, SearchType
from src.config.config_manager import ConfigManager


async def main():
    """主演示函数"""
    print("🚀 完整LLM增强搜索系统演示")
    print("=" * 60)
    
    # 初始化系统
    config_manager = ConfigManager("config/search_config.yaml")
    config = config_manager.get_config()
    
    search_manager = EnhancedSearchManager(config)
    feedback_system = FeedbackSystem(config.get('feedback', {}))
    
    try:
        # 初始化组件
        await search_manager.initialize()
        await feedback_system.initialize()
        print("✅ 系统初始化完成\n")
        
        # 1. 文档预处理和索引
        await demo_document_processing(search_manager)
        
        # 2. 智能搜索演示
        await demo_intelligent_search(search_manager)
        
        # 3. 反馈收集演示
        await demo_feedback_collection(search_manager, feedback_system)
        
        # 4. 学习和优化演示
        await demo_learning_optimization(feedback_system)
        
        # 5. 系统分析演示
        await demo_system_analytics(search_manager, feedback_system)
        
    except Exception as e:
        print(f"❌ 演示过程中出现错误: {e}")
    
    finally:
        await search_manager.cleanup()
        print("\n🎯 演示完成！")


async def demo_document_processing(search_manager: EnhancedSearchManager):
    """演示文档处理"""
    print("📚 1. 智能文档处理演示")
    print("-" * 40)
    
    # 准备多样化的文档
    documents = [
        {
            'id': 'tech_ai_001',
            'content': '''
            人工智能技术发展历程
            
            人工智能（AI）的发展可以追溯到20世纪50年代。1950年，艾伦·图灵提出了著名的"图灵测试"，
            为判断机器是否具有智能提供了标准。
            
            发展阶段：
            1. 符号主义时期（1950s-1980s）：基于逻辑推理和符号处理
            2. 连接主义复兴（1980s-2000s）：神经网络的重新兴起
            3. 深度学习时代（2000s-至今）：大数据和计算能力推动的突破
            
            关键技术：
            - 机器学习：让机器从数据中学习模式
            - 深度学习：模拟人脑神经网络的多层结构
            - 自然语言处理：让机器理解和生成人类语言
            - 计算机视觉：让机器"看懂"图像和视频
            
            应用领域：
            - 医疗诊断：辅助医生进行疾病诊断
            - 自动驾驶：实现无人驾驶汽车
            - 金融风控：识别欺诈和评估风险
            - 智能客服：提供24/7客户服务
            ''',
            'metadata': {
                'category': 'technology',
                'subcategory': 'artificial_intelligence',
                'tags': ['AI', 'machine-learning', 'deep-learning', 'history'],
                'author': 'Tech Expert',
                'created_at': '2024-01-15T10:00:00',
                'language': 'zh'
            }
        },
        {
            'id': 'prog_python_001',
            'content': '''
            Python编程最佳实践指南
            
            Python是一种优雅、简洁的编程语言，以其可读性和易用性而闻名。
            以下是一些Python编程的最佳实践：
            
            代码风格：
            1. 遵循PEP 8编码规范
            2. 使用有意义的变量名和函数名
            3. 保持函数简短，单一职责
            4. 适当添加注释和文档字符串
            
            性能优化：
            - 使用列表推导式而非循环
            - 合理使用生成器节省内存
            - 选择合适的数据结构
            - 避免过早优化
            
            错误处理：
            - 使用try-except处理异常
            - 具体化异常类型
            - 记录错误日志
            - 优雅地处理边界情况
            
            测试：
            - 编写单元测试
            - 使用pytest框架
            - 保持高测试覆盖率
            - 持续集成和部署
            
            常用库：
            - NumPy：数值计算
            - Pandas：数据分析
            - Matplotlib：数据可视化
            - Requests：HTTP请求
            - Django/Flask：Web开发
            ''',
            'metadata': {
                'category': 'programming',
                'subcategory': 'python',
                'tags': ['python', 'best-practices', 'coding', 'development'],
                'author': 'Python Developer',
                'created_at': '2024-01-16T14:30:00',
                'language': 'zh'
            }
        },
        {
            'id': 'data_science_001',
            'content': '''
            数据科学项目完整流程
            
            数据科学项目通常遵循CRISP-DM（跨行业数据挖掘标准流程）方法论：
            
            1. 业务理解（Business Understanding）
            - 定义项目目标和成功标准
            - 评估现状和资源
            - 制定项目计划
            
            2. 数据理解（Data Understanding）
            - 收集初始数据
            - 描述数据特征
            - 探索数据质量
            - 发现数据洞察
            
            3. 数据准备（Data Preparation）
            - 数据清洗：处理缺失值、异常值
            - 数据转换：标准化、归一化
            - 特征工程：创建新特征
            - 数据集成：合并多个数据源
            
            4. 建模（Modeling）
            - 选择建模技术
            - 设计测试方案
            - 构建模型
            - 评估模型性能
            
            5. 评估（Evaluation）
            - 评估结果质量
            - 回顾过程
            - 确定下一步行动
            
            6. 部署（Deployment）
            - 制定部署计划
            - 监控和维护
            - 产出最终报告
            - 项目回顾
            
            关键技能：
            - 统计学基础
            - 编程能力（Python/R）
            - 机器学习算法
            - 数据可视化
            - 业务理解能力
            ''',
            'metadata': {
                'category': 'data_science',
                'subcategory': 'methodology',
                'tags': ['data-science', 'CRISP-DM', 'methodology', 'process'],
                'author': 'Data Scientist',
                'created_at': '2024-01-17T09:15:00',
                'language': 'zh'
            }
        }
    ]
    
    # 使用LLM预处理文档
    success = await search_manager.add_documents_with_processing(documents)
    
    if success:
        print("✅ 文档处理完成")
        print(f"   - 处理了 {len(documents)} 个文档")
        print("   - 生成了智能分块、摘要、关键词和语义标签")
        print("   - 构建了多级向量索引")
    else:
        print("❌ 文档处理失败")
    
    print()


async def demo_intelligent_search(search_manager: EnhancedSearchManager):
    """演示智能搜索"""
    print("🔍 2. 智能搜索演示")
    print("-" * 40)
    
    # 测试不同类型的查询
    test_queries = [
        {
            'query': '人工智能的发展历史',
            'description': '历史性查询'
        },
        {
            'query': 'Python编程有什么优势？',
            'description': '比较性查询'
        },
        {
            'query': '如何进行数据科学项目？',
            'description': '过程性查询'
        },
        {
            'query': '机器学习和深度学习的区别',
            'description': '概念性查询'
        }
    ]
    
    for i, test_case in enumerate(test_queries, 1):
        print(f"\n查询 {i}: {test_case['query']} ({test_case['description']})")
        print("-" * 30)
        
        query = SearchQuery(
            query=test_case['query'],
            search_type=SearchType.HYBRID,
            limit=3
        )
        
        # 执行增强搜索
        response = await search_manager.enhanced_search(query)
        
        # 显示查询分析
        if response.get('query_analysis'):
            analysis = response['query_analysis']
            print(f"意图识别: {analysis.intent.value}")
            print(f"关键词: {', '.join(analysis.keywords[:3])}")
            print(f"扩展查询: {', '.join(analysis.expanded_queries[:2])}")
        
        # 显示搜索结果
        print(f"\n找到 {response['total_results']} 个结果:")
        for j, result in enumerate(response['results'][:2], 1):
            print(f"  {j}. 分数: {result.score:.3f}")
            print(f"     来源: {result.source}")
            print(f"     内容: {result.content[:100]}...")
        
        # 显示生成的答案
        if response.get('generated_answer'):
            answer = response['generated_answer']
            print(f"\n💡 生成答案:")
            print(f"   {answer.get('answer', '无法生成答案')[:150]}...")
            
            if answer.get('key_points'):
                print(f"   关键要点: {', '.join(answer['key_points'][:2])}")
    
    print()


async def demo_feedback_collection(search_manager: EnhancedSearchManager, 
                                 feedback_system: FeedbackSystem):
    """演示反馈收集"""
    print("📝 3. 用户反馈收集演示")
    print("-" * 40)
    
    # 模拟用户搜索和反馈
    search_scenarios = [
        {
            'query': 'Python数据分析',
            'feedback': {
                'type': FeedbackType.RELEVANCE,
                'rating': FeedbackRating.GOOD,
                'comment': '结果很相关，找到了需要的信息'
            }
        },
        {
            'query': '机器学习算法',
            'feedback': {
                'type': FeedbackType.QUALITY,
                'rating': FeedbackRating.VERY_GOOD,
                'comment': '内容质量很高，解释清晰'
            }
        },
        {
            'query': '深度学习入门',
            'feedback': {
                'type': FeedbackType.USEFULNESS,
                'rating': FeedbackRating.BAD,
                'comment': '内容太复杂，不适合初学者'
            }
        },
        {
            'query': 'AI应用案例',
            'feedback': {
                'type': FeedbackType.ACCURACY,
                'rating': FeedbackRating.NEUTRAL,
                'comment': '信息有些过时，需要更新'
            }
        }
    ]
    
    for scenario in search_scenarios:
        # 执行搜索
        query = SearchQuery(query=scenario['query'], limit=2)
        response = await search_manager.enhanced_search(query)
        
        if response['results']:
            # 收集反馈
            feedback = UserFeedback(
                id=str(uuid.uuid4()),
                query=scenario['query'],
                result_id=response['results'][0].id,
                feedback_type=scenario['feedback']['type'],
                rating=scenario['feedback']['rating'],
                comment=scenario['feedback']['comment'],
                user_id=f"user_{hash(scenario['query']) % 1000}",
                session_id=str(uuid.uuid4())
            )
            
            success = await feedback_system.collect_feedback(feedback)
            if success:
                print(f"✅ 收集反馈: {scenario['query']} - {scenario['feedback']['rating'].name}")
            else:
                print(f"❌ 反馈收集失败: {scenario['query']}")
    
    print()


async def demo_learning_optimization(feedback_system: FeedbackSystem):
    """演示学习和优化"""
    print("🧠 4. 学习和优化演示")
    print("-" * 40)
    
    # 获取性能指标
    metrics = await feedback_system.get_performance_metrics()
    
    print("系统性能指标:")
    print(f"  总反馈数: {metrics.get('total_feedback', 0)}")
    print(f"  平均评分: {metrics.get('average_rating', 0):.2f}")
    
    # 显示最差查询
    worst_queries = metrics.get('worst_queries', [])
    if worst_queries:
        print(f"\n需要改进的查询:")
        for query_info in worst_queries[:3]:
            print(f"  - {query_info['query']} (评分: {query_info['avg_rating']:.2f})")
    
    # 显示最佳查询
    best_queries = metrics.get('best_queries', [])
    if best_queries:
        print(f"\n表现良好的查询:")
        for query_info in best_queries[:3]:
            print(f"  - {query_info['query']} (评分: {query_info['avg_rating']:.2f})")
    
    # 获取优化建议
    if worst_queries:
        worst_query = worst_queries[0]['query']
        suggestions = await feedback_system.get_query_optimization_suggestions(worst_query)
        
        if suggestions['suggestions']:
            print(f"\n💡 针对查询 '{worst_query}' 的优化建议:")
            sugg = suggestions['suggestions']
            if sugg.get('rewritten_queries'):
                print(f"  重写建议: {', '.join(sugg['rewritten_queries'][:2])}")
            if sugg.get('strategy_adjustments'):
                print(f"  策略调整: {', '.join(sugg['strategy_adjustments'][:2])}")
    
    # 显示学习洞察
    insights = await feedback_system.get_recent_insights(3)
    if insights:
        print(f"\n🔍 最近的学习洞察:")
        for insight in insights:
            print(f"  - {insight['pattern']} (置信度: {insight['confidence']:.2f})")
            print(f"    建议: {insight['recommendation'][:100]}...")
    
    print()


async def demo_system_analytics(search_manager: EnhancedSearchManager, 
                               feedback_system: FeedbackSystem):
    """演示系统分析"""
    print("📊 5. 系统分析演示")
    print("-" * 40)
    
    # 搜索系统分析
    analytics = await search_manager.get_analytics()
    
    print("搜索系统状态:")
    print(f"  总查询数: {analytics.get('total_queries', 0)}")
    print(f"  缓存命中率: {analytics.get('cache_hit_rate', 0):.1%}")
    
    print("\n数据源状态:")
    for ds_status in analytics.get('data_sources_status', []):
        status = "✅ 健康" if ds_status['healthy'] else "❌ 异常"
        print(f"  {ds_status['name']} ({ds_status['type']}): {status}")
    
    # 反馈系统分析
    feedback_metrics = await feedback_system.get_performance_metrics()
    
    print(f"\n反馈系统统计:")
    print(f"  总反馈数: {feedback_metrics.get('total_feedback', 0)}")
    print(f"  平均满意度: {feedback_metrics.get('average_rating', 0):.2f}/5.0")
    print(f"  学习洞察数: {feedback_metrics.get('insights_count', 0)}")
    
    # 按类型统计
    type_stats = feedback_metrics.get('type_statistics', [])
    if type_stats:
        print(f"\n按反馈类型统计:")
        for stat in type_stats:
            print(f"  {stat['type']}: {stat['count']} 条 (平均: {stat['avg_rating']:.2f})")
    
    print()


if __name__ == "__main__":
    asyncio.run(main())
