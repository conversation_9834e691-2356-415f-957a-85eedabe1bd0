"""
大模型驱动的结果优化和重排序
"""
import logging
import asyncio
import json
from typing import List, Dict, Any, Optional, Tuple
from dataclasses import dataclass

from langchain_openai import ChatOpenAI
from langchain.prompts import PromptTemplate
from ..core.interfaces import SearchResult, SearchQuery

logger = logging.getLogger(__name__)


@dataclass
class OptimizedResult:
    """优化后的搜索结果"""
    original_result: SearchResult
    relevance_score: float
    quality_score: float
    final_score: float
    explanation: str
    generated_answer: Optional[str] = None
    key_points: Optional[List[str]] = None


class LLMResultOptimizer:
    """大模型驱动的结果优化器"""
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.llm = ChatOpenAI(
            model=config.get('model', 'gpt-3.5-turbo'),
            temperature=config.get('temperature', 0.1),
            max_tokens=config.get('max_tokens', 1500)
        )
        
        self.relevance_weight = config.get('relevance_weight', 0.6)
        self.quality_weight = config.get('quality_weight', 0.4)
        
        self._init_prompts()
    
    def _init_prompts(self):
        """初始化提示模板"""
        
        # 相关性评估提示
        self.relevance_prompt = PromptTemplate(
            input_variables=["query", "content"],
            template="""请评估以下内容与查询的相关性，返回0-1之间的分数和简短解释：

查询：{query}

内容：{content}

请从以下角度评估：
1. 内容是否直接回答了查询问题
2. 内容与查询主题的匹配度
3. 内容的完整性和准确性

返回JSON格式：
{{
    "relevance_score": 0.85,
    "explanation": "评估解释"
}}"""
        )
        
        # 质量评估提示
        self.quality_prompt = PromptTemplate(
            input_variables=["content"],
            template="""请评估以下内容的质量，返回0-1之间的分数和评估理由：

内容：{content}

评估标准：
1. 信息的准确性和可信度
2. 内容的完整性和结构性
3. 语言表达的清晰度
4. 信息的时效性和实用性

返回JSON格式：
{{
    "quality_score": 0.80,
    "explanation": "质量评估理由"
}}"""
        )
        
        # 答案生成提示
        self.answer_prompt = PromptTemplate(
            input_variables=["query", "contents"],
            template="""基于以下检索到的内容，为用户查询生成一个准确、完整的答案：

用户查询：{query}

检索内容：
{contents}

请生成：
1. 直接回答用户问题的答案
2. 提取3-5个关键要点
3. 确保答案基于提供的内容，不要添加额外信息

返回JSON格式：
{{
    "answer": "生成的答案",
    "key_points": ["要点1", "要点2", "要点3"]
}}"""
        )
        
        # 结果排序提示
        self.ranking_prompt = PromptTemplate(
            input_variables=["query", "results_info"],
            template="""请为以下搜索结果重新排序，使最相关和最有用的结果排在前面：

查询：{query}

结果信息：
{results_info}

请考虑：
1. 与查询的相关性
2. 内容的质量和可信度
3. 信息的完整性
4. 实用性和时效性

返回排序后的结果ID列表：
["result_id_1", "result_id_2", "result_id_3", ...]"""
        )
    
    async def optimize_results(self, results: List[SearchResult], 
                             query: SearchQuery) -> List[OptimizedResult]:
        """优化搜索结果"""
        if not results:
            return []
        
        try:
            # 并行评估所有结果
            tasks = []
            for result in results:
                task = self._evaluate_result(result, query.query)
                tasks.append(task)
            
            optimized_results = await asyncio.gather(*tasks, return_exceptions=True)
            
            # 过滤异常结果
            valid_results = []
            for i, opt_result in enumerate(optimized_results):
                if isinstance(opt_result, OptimizedResult):
                    valid_results.append(opt_result)
                else:
                    logger.error(f"Result optimization failed for result {i}: {opt_result}")
                    # 创建默认优化结果
                    default_result = self._create_default_optimized_result(results[i])
                    valid_results.append(default_result)
            
            # 重新排序
            sorted_results = await self._rerank_results(valid_results, query.query)
            
            logger.info(f"Optimized {len(valid_results)} results")
            return sorted_results
            
        except Exception as e:
            logger.error(f"Results optimization failed: {e}")
            # 返回默认优化结果
            return [self._create_default_optimized_result(result) for result in results]
    
    async def _evaluate_result(self, result: SearchResult, query: str) -> OptimizedResult:
        """评估单个结果"""
        try:
            # 并行评估相关性和质量
            relevance_task = self._evaluate_relevance(query, result.content)
            quality_task = self._evaluate_quality(result.content)
            
            relevance_data, quality_data = await asyncio.gather(
                relevance_task, quality_task, return_exceptions=True
            )
            
            # 处理评估结果
            if isinstance(relevance_data, Exception):
                logger.warning(f"Relevance evaluation failed: {relevance_data}")
                relevance_score, relevance_explanation = 0.5, "评估失败"
            else:
                relevance_score = relevance_data.get('relevance_score', 0.5)
                relevance_explanation = relevance_data.get('explanation', '')
            
            if isinstance(quality_data, Exception):
                logger.warning(f"Quality evaluation failed: {quality_data}")
                quality_score, quality_explanation = 0.5, "评估失败"
            else:
                quality_score = quality_data.get('quality_score', 0.5)
                quality_explanation = quality_data.get('explanation', '')
            
            # 计算最终分数
            final_score = (relevance_score * self.relevance_weight + 
                          quality_score * self.quality_weight)
            
            # 结合原始分数
            if hasattr(result, 'score') and result.score > 0:
                final_score = (final_score + result.score) / 2
            
            explanation = f"相关性: {relevance_explanation}; 质量: {quality_explanation}"
            
            return OptimizedResult(
                original_result=result,
                relevance_score=relevance_score,
                quality_score=quality_score,
                final_score=final_score,
                explanation=explanation
            )
            
        except Exception as e:
            logger.error(f"Result evaluation failed: {e}")
            return self._create_default_optimized_result(result)
    
    async def _evaluate_relevance(self, query: str, content: str) -> Dict[str, Any]:
        """评估相关性"""
        try:
            # 限制内容长度
            content = content[:1500] if len(content) > 1500 else content
            
            prompt = self.relevance_prompt.format(query=query, content=content)
            response = await self.llm.ainvoke(prompt)
            
            # 解析JSON响应
            try:
                result = json.loads(response.content.strip())
                return result
            except json.JSONDecodeError:
                # 解析失败时使用默认值
                return {'relevance_score': 0.5, 'explanation': '解析失败'}
                
        except Exception as e:
            logger.error(f"Relevance evaluation failed: {e}")
            return {'relevance_score': 0.5, 'explanation': '评估失败'}
    
    async def _evaluate_quality(self, content: str) -> Dict[str, Any]:
        """评估质量"""
        try:
            # 限制内容长度
            content = content[:1500] if len(content) > 1500 else content
            
            prompt = self.quality_prompt.format(content=content)
            response = await self.llm.ainvoke(prompt)
            
            # 解析JSON响应
            try:
                result = json.loads(response.content.strip())
                return result
            except json.JSONDecodeError:
                return {'quality_score': 0.5, 'explanation': '解析失败'}
                
        except Exception as e:
            logger.error(f"Quality evaluation failed: {e}")
            return {'quality_score': 0.5, 'explanation': '评估失败'}
    
    async def _rerank_results(self, results: List[OptimizedResult], 
                            query: str) -> List[OptimizedResult]:
        """重新排序结果"""
        try:
            # 如果结果较少，直接按分数排序
            if len(results) <= 3:
                return sorted(results, key=lambda x: x.final_score, reverse=True)
            
            # 准备结果信息用于LLM排序
            results_info = []
            for i, result in enumerate(results):
                info = {
                    'id': f"result_{i}",
                    'content_preview': result.original_result.content[:200],
                    'score': result.final_score,
                    'source': result.original_result.source
                }
                results_info.append(f"ID: {info['id']}, 分数: {info['score']:.2f}, "
                                  f"来源: {info['source']}, 内容: {info['content_preview']}...")
            
            results_info_str = '\n'.join(results_info)
            
            prompt = self.ranking_prompt.format(
                query=query, 
                results_info=results_info_str
            )
            response = await self.llm.ainvoke(prompt)
            
            # 解析排序结果
            try:
                # 尝试解析JSON数组
                ordered_ids = json.loads(response.content.strip())
                
                # 根据LLM的排序重新组织结果
                reordered_results = []
                used_indices = set()
                
                for result_id in ordered_ids:
                    if isinstance(result_id, str) and result_id.startswith('result_'):
                        try:
                            index = int(result_id.split('_')[1])
                            if 0 <= index < len(results) and index not in used_indices:
                                reordered_results.append(results[index])
                                used_indices.add(index)
                        except (ValueError, IndexError):
                            continue
                
                # 添加未被排序的结果
                for i, result in enumerate(results):
                    if i not in used_indices:
                        reordered_results.append(result)
                
                return reordered_results
                
            except (json.JSONDecodeError, TypeError):
                # LLM排序失败，使用分数排序
                return sorted(results, key=lambda x: x.final_score, reverse=True)
                
        except Exception as e:
            logger.error(f"Results reranking failed: {e}")
            # 回退到分数排序
            return sorted(results, key=lambda x: x.final_score, reverse=True)
    
    async def generate_answer(self, results: List[OptimizedResult], 
                            query: str) -> Optional[Dict[str, Any]]:
        """基于结果生成答案"""
        if not results:
            return None
        
        try:
            # 选择前几个最相关的结果
            top_results = results[:3]
            contents = []
            
            for i, result in enumerate(top_results):
                content = f"来源{i+1}: {result.original_result.content[:800]}"
                contents.append(content)
            
            contents_str = '\n\n'.join(contents)
            
            prompt = self.answer_prompt.format(query=query, contents=contents_str)
            response = await self.llm.ainvoke(prompt)
            
            # 解析响应
            try:
                answer_data = json.loads(response.content.strip())
                return answer_data
            except json.JSONDecodeError:
                # 如果JSON解析失败，返回原始文本
                return {
                    'answer': response.content.strip(),
                    'key_points': []
                }
                
        except Exception as e:
            logger.error(f"Answer generation failed: {e}")
            return None
    
    def _create_default_optimized_result(self, result: SearchResult) -> OptimizedResult:
        """创建默认优化结果"""
        return OptimizedResult(
            original_result=result,
            relevance_score=0.5,
            quality_score=0.5,
            final_score=result.score if hasattr(result, 'score') else 0.5,
            explanation="使用默认评估"
        )
