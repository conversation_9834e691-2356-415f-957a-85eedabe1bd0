"""
大模型驱动的查询理解和扩展
"""
import logging
import asyncio
import json
from typing import List, Dict, Any, Optional, Tuple
from dataclasses import dataclass
from enum import Enum

from langchain_openai import ChatOpenAI
from langchain.prompts import PromptTemplate
from langchain.schema import BaseMessage, HumanMessage, SystemMessage
from .model_adapter import create_model_adapter, BaseModelAdapter

logger = logging.getLogger(__name__)


class QueryIntent(Enum):
    """查询意图枚举"""
    FACTUAL = "factual"  # 事实查询
    CONCEPTUAL = "conceptual"  # 概念查询
    PROCEDURAL = "procedural"  # 过程查询
    COMPARATIVE = "comparative"  # 比较查询
    ANALYTICAL = "analytical"  # 分析查询


@dataclass
class QueryAnalysis:
    """查询分析结果"""
    original_query: str
    intent: QueryIntent
    expanded_queries: List[str]
    keywords: List[str]
    entities: List[str]
    synonyms: Dict[str, List[str]]
    language: str
    confidence: float
    suggested_filters: List[Dict[str, Any]]


class LLMQueryProcessor:
    """大模型驱动的查询处理器"""

    def __init__(self, config: Dict[str, Any]):
        self.config = config

        # 使用模型适配器支持多种模型
        provider = config.get('provider', 'openai')
        if provider == 'openai' and 'provider' not in config:
            # 向后兼容：如果没有指定provider，使用原来的ChatOpenAI
            self.llm = ChatOpenAI(
                model=config.get('model', 'gpt-3.5-turbo'),
                temperature=config.get('temperature', 0.1),
                max_tokens=config.get('max_tokens', 1000)
            )
            self.use_adapter = False
        else:
            # 使用新的模型适配器
            self.llm = create_model_adapter(config)
            self.use_adapter = True

        self._init_prompts()

    async def _invoke_model(self, prompt: str) -> str:
        """统一的模型调用方法"""
        if self.use_adapter:
            return await self.llm.ainvoke(prompt)
        else:
            response = await self.llm.ainvoke(prompt)
            return response.content

    def _init_prompts(self):
        """初始化提示模板"""
        
        # 查询分析提示
        self.analysis_prompt = PromptTemplate(
            input_variables=["query"],
            template="""请分析以下查询的意图和特征，返回JSON格式的结果：

查询：{query}

请分析：
1. 查询意图（factual/conceptual/procedural/comparative/analytical）
2. 关键词提取
3. 实体识别
4. 语言检测
5. 置信度评估（0-1）

返回JSON格式：
{{
    "intent": "查询意图",
    "keywords": ["关键词1", "关键词2"],
    "entities": ["实体1", "实体2"],
    "language": "语言代码",
    "confidence": 0.95
}}"""
        )
        
        # 查询扩展提示
        self.expansion_prompt = PromptTemplate(
            input_variables=["query", "intent"],
            template="""基于原始查询和意图，生成3-5个相关的扩展查询，用于提高检索召回率：

原始查询：{query}
查询意图：{intent}

扩展查询应该：
1. 保持原意但使用不同表达方式
2. 包含同义词和相关概念
3. 考虑不同的问法角度

扩展查询（每行一个）："""
        )
        
        # 同义词生成提示
        self.synonyms_prompt = PromptTemplate(
            input_variables=["keywords"],
            template="""为以下关键词生成同义词，返回JSON格式：

关键词：{keywords}

返回格式：
{{
    "关键词1": ["同义词1", "同义词2"],
    "关键词2": ["同义词1", "同义词2"]
}}"""
        )
        
        # 筛选条件建议提示
        self.filters_prompt = PromptTemplate(
            input_variables=["query", "intent"],
            template="""基于查询内容和意图，建议可能有用的筛选条件：

查询：{query}
意图：{intent}

请建议筛选条件，返回JSON格式：
{{
    "suggested_filters": [
        {{"field": "category", "operator": "eq", "value": "建议值", "reason": "原因"}},
        {{"field": "date", "operator": "gte", "value": "建议值", "reason": "原因"}}
    ]
}}"""
        )
    
    async def analyze_query(self, query: str) -> QueryAnalysis:
        """分析查询"""
        try:
            # 并行执行多个分析任务
            tasks = [
                self._analyze_intent_and_features(query),
                self._expand_query(query),
                self._extract_keywords_and_entities(query)
            ]
            
            analysis_result, expanded_queries, keywords_entities = await asyncio.gather(
                *tasks, return_exceptions=True
            )
            
            # 处理分析结果
            if isinstance(analysis_result, Exception):
                logger.error(f"Query analysis failed: {analysis_result}")
                analysis_result = self._default_analysis(query)
            
            if isinstance(expanded_queries, Exception):
                logger.error(f"Query expansion failed: {expanded_queries}")
                expanded_queries = [query]
            
            if isinstance(keywords_entities, Exception):
                logger.error(f"Keywords extraction failed: {keywords_entities}")
                keywords_entities = ([], [])
            
            keywords, entities = keywords_entities
            
            # 生成同义词
            synonyms = await self._generate_synonyms(keywords)
            if isinstance(synonyms, Exception):
                logger.error(f"Synonyms generation failed: {synonyms}")
                synonyms = {}
            
            # 建议筛选条件
            suggested_filters = await self._suggest_filters(query, analysis_result.get('intent', 'factual'))
            if isinstance(suggested_filters, Exception):
                logger.error(f"Filter suggestion failed: {suggested_filters}")
                suggested_filters = []
            
            return QueryAnalysis(
                original_query=query,
                intent=QueryIntent(analysis_result.get('intent', 'factual')),
                expanded_queries=expanded_queries,
                keywords=keywords,
                entities=entities,
                synonyms=synonyms,
                language=analysis_result.get('language', 'zh'),
                confidence=analysis_result.get('confidence', 0.8),
                suggested_filters=suggested_filters
            )
            
        except Exception as e:
            logger.error(f"Query analysis failed: {e}")
            return self._default_query_analysis(query)
    
    async def _analyze_intent_and_features(self, query: str) -> Dict[str, Any]:
        """分析查询意图和特征"""
        try:
            prompt = self.analysis_prompt.format(query=query)
            response_content = await self._invoke_model(prompt)

            # 尝试解析JSON响应
            try:
                result = json.loads(response_content.strip())
                return result
            except json.JSONDecodeError:
                # 如果JSON解析失败，使用正则表达式提取信息
                return self._parse_analysis_response(response.content)
                
        except Exception as e:
            logger.error(f"Intent analysis failed: {e}")
            return self._default_analysis(query)
    
    async def _expand_query(self, query: str, intent: str = "factual") -> List[str]:
        """扩展查询"""
        try:
            prompt = self.expansion_prompt.format(query=query, intent=intent)
            response_content = await self._invoke_model(prompt)

            # 解析扩展查询
            expanded_queries = []
            for line in response_content.strip().split('\n'):
                line = line.strip()
                if line and not line.startswith('扩展查询') and not line.startswith('#'):
                    # 移除序号
                    line = re.sub(r'^\d+\.\s*', '', line)
                    expanded_queries.append(line)
            
            return expanded_queries[:5]  # 限制数量
            
        except Exception as e:
            logger.error(f"Query expansion failed: {e}")
            return [query]
    
    async def _extract_keywords_and_entities(self, query: str) -> Tuple[List[str], List[str]]:
        """提取关键词和实体"""
        try:
            # 简化实现：使用基础的文本处理
            import re
            import jieba
            
            # 中文分词
            words = list(jieba.cut(query))
            
            # 过滤停用词和标点
            stop_words = {'的', '是', '在', '有', '和', '与', '或', '但', '而', '了', '吗', '呢', '吧'}
            keywords = [word for word in words if len(word) > 1 and word not in stop_words]
            
            # 简单的实体识别（可以后续集成NER模型）
            entities = []
            # 识别可能的实体模式
            entity_patterns = [
                r'[A-Z][a-zA-Z]+',  # 英文专有名词
                r'\d{4}年',  # 年份
                r'[0-9]+[%％]',  # 百分比
            ]
            
            for pattern in entity_patterns:
                matches = re.findall(pattern, query)
                entities.extend(matches)
            
            return keywords[:10], entities[:5]  # 限制数量
            
        except Exception as e:
            logger.error(f"Keywords extraction failed: {e}")
            return [], []
    
    async def _generate_synonyms(self, keywords: List[str]) -> Dict[str, List[str]]:
        """生成同义词"""
        if not keywords:
            return {}
        
        try:
            keywords_str = ', '.join(keywords[:5])  # 限制关键词数量
            prompt = self.synonyms_prompt.format(keywords=keywords_str)
            response_content = await self._invoke_model(prompt)

            # 尝试解析JSON
            try:
                synonyms = json.loads(response_content.strip())
                return synonyms
            except json.JSONDecodeError:
                # 解析失败时返回空字典
                return {}
                
        except Exception as e:
            logger.error(f"Synonyms generation failed: {e}")
            return {}
    
    async def _suggest_filters(self, query: str, intent: str) -> List[Dict[str, Any]]:
        """建议筛选条件"""
        try:
            prompt = self.filters_prompt.format(query=query, intent=intent)
            response_content = await self._invoke_model(prompt)

            # 尝试解析JSON
            try:
                result = json.loads(response_content.strip())
                return result.get('suggested_filters', [])
            except json.JSONDecodeError:
                return []
                
        except Exception as e:
            logger.error(f"Filter suggestion failed: {e}")
            return []
    
    def _parse_analysis_response(self, response: str) -> Dict[str, Any]:
        """解析分析响应（当JSON解析失败时）"""
        result = {
            'intent': 'factual',
            'keywords': [],
            'entities': [],
            'language': 'zh',
            'confidence': 0.8
        }
        
        # 简单的文本解析逻辑
        lines = response.split('\n')
        for line in lines:
            line = line.strip().lower()
            if 'intent' in line or '意图' in line:
                if 'conceptual' in line or '概念' in line:
                    result['intent'] = 'conceptual'
                elif 'procedural' in line or '过程' in line:
                    result['intent'] = 'procedural'
                elif 'comparative' in line or '比较' in line:
                    result['intent'] = 'comparative'
                elif 'analytical' in line or '分析' in line:
                    result['intent'] = 'analytical'
        
        return result
    
    def _default_analysis(self, query: str) -> Dict[str, Any]:
        """默认分析结果"""
        return {
            'intent': 'factual',
            'keywords': query.split()[:5],
            'entities': [],
            'language': 'zh' if any('\u4e00' <= char <= '\u9fff' for char in query) else 'en',
            'confidence': 0.6
        }
    
    def _default_query_analysis(self, query: str) -> QueryAnalysis:
        """默认查询分析结果"""
        return QueryAnalysis(
            original_query=query,
            intent=QueryIntent.FACTUAL,
            expanded_queries=[query],
            keywords=query.split()[:5],
            entities=[],
            synonyms={},
            language='zh' if any('\u4e00' <= char <= '\u9fff' for char in query) else 'en',
            confidence=0.6,
            suggested_filters=[]
        )
    
    async def process_multi_language_query(self, query: str, target_language: str = 'zh') -> str:
        """处理多语言查询"""
        try:
            if target_language == 'zh':
                translate_prompt = f"请将以下查询翻译成中文：{query}"
            else:
                translate_prompt = f"Please translate the following query to English: {query}"

            response_content = await self._invoke_model(translate_prompt)
            return response_content.strip()
            
        except Exception as e:
            logger.error(f"Translation failed: {e}")
            return query
