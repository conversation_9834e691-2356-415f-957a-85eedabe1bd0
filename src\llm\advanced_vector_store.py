"""
高级向量存储和索引优化
"""
import logging
import asyncio
import numpy as np
from typing import List, Dict, Any, Optional, Tuple
from dataclasses import dataclass
import json

import faiss
from sentence_transformers import SentenceTransformer
from transformers import AutoTokenizer, AutoModel
import torch
from sklearn.cluster import KMeans
from sklearn.metrics.pairwise import cosine_similarity

from ..core.interfaces import DataSource, SearchQuery, SearchResult, SearchType

logger = logging.getLogger(__name__)


@dataclass
class VectorDocument:
    """向量文档数据结构"""
    id: str
    content: str
    vector: np.ndarray
    metadata: Dict[str, Any]
    cluster_id: Optional[int] = None
    summary_vector: Optional[np.ndarray] = None


class AdvancedVectorStore(DataSource):
    """高级向量存储"""
    
    def __init__(self, config: Dict[str, Any]):
        super().__init__(config)
        
        # 模型配置
        self.primary_model_name = config.get('primary_model', 'all-MiniLM-L6-v2')
        self.secondary_model_name = config.get('secondary_model', 'paraphrase-multilingual-MiniLM-L12-v2')
        
        # 索引配置
        self.dimension = config.get('dimension', 384)
        self.use_gpu = config.get('use_gpu', False)
        self.cluster_count = config.get('cluster_count', 100)
        self.enable_hierarchical_index = config.get('enable_hierarchical_index', True)
        
        # 模型和索引
        self.primary_model = None
        self.secondary_model = None
        self.faiss_index = None
        self.cluster_index = None
        self.documents: List[VectorDocument] = []
        self.clusters = {}
        
        # 连接状态
        self.connected = False
    
    async def connect(self) -> bool:
        """连接向量存储"""
        try:
            # 加载主要嵌入模型
            self.primary_model = SentenceTransformer(self.primary_model_name)
            logger.info(f"Loaded primary model: {self.primary_model_name}")
            
            # 加载辅助模型（用于多语言和特殊场景）
            self.secondary_model = SentenceTransformer(self.secondary_model_name)
            logger.info(f"Loaded secondary model: {self.secondary_model_name}")
            
            # 初始化FAISS索引
            self._initialize_faiss_index()
            
            self.connected = True
            logger.info("Advanced vector store connected successfully")
            return True
            
        except Exception as e:
            logger.error(f"Failed to connect to advanced vector store: {e}")
            return False
    
    def _initialize_faiss_index(self):
        """初始化FAISS索引"""
        if self.use_gpu and faiss.get_num_gpus() > 0:
            # GPU索引
            res = faiss.StandardGpuResources()
            self.faiss_index = faiss.GpuIndexFlatIP(res, self.dimension)
            logger.info("Initialized GPU FAISS index")
        else:
            # CPU索引 - 使用IVF索引提高性能
            quantizer = faiss.IndexFlatIP(self.dimension)
            self.faiss_index = faiss.IndexIVFFlat(quantizer, self.dimension, min(self.cluster_count, 100))
            logger.info("Initialized CPU FAISS IVF index")
    
    async def disconnect(self) -> None:
        """断开连接"""
        self.connected = False
        self.documents.clear()
        if self.faiss_index:
            self.faiss_index.reset()
        logger.info("Disconnected from advanced vector store")
    
    async def search(self, query: SearchQuery) -> List[SearchResult]:
        """执行向量搜索"""
        if not self.connected or not self.documents:
            return []
        
        try:
            # 1. 生成查询向量
            query_vectors = await self._generate_query_vectors(query.query)
            
            # 2. 执行多级搜索
            if self.enable_hierarchical_index and len(self.documents) > 1000:
                results = await self._hierarchical_search(query, query_vectors)
            else:
                results = await self._flat_search(query, query_vectors)
            
            # 3. 后处理和过滤
            filtered_results = self._post_process_results(results, query)
            
            return filtered_results[:query.limit]
            
        except Exception as e:
            logger.error(f"Vector search failed: {e}")
            return []
    
    async def _generate_query_vectors(self, query: str) -> Dict[str, np.ndarray]:
        """生成查询向量"""
        vectors = {}
        
        try:
            # 主要模型向量
            primary_vector = self.primary_model.encode([query], normalize_embeddings=True)[0]
            vectors['primary'] = primary_vector
            
            # 辅助模型向量（用于多语言查询）
            if self._is_multilingual_query(query):
                secondary_vector = self.secondary_model.encode([query], normalize_embeddings=True)[0]
                vectors['secondary'] = secondary_vector
            
            return vectors
            
        except Exception as e:
            logger.error(f"Query vector generation failed: {e}")
            return {'primary': np.zeros(self.dimension)}
    
    def _is_multilingual_query(self, query: str) -> bool:
        """检测是否为多语言查询"""
        # 简单的多语言检测
        has_chinese = any('\u4e00' <= char <= '\u9fff' for char in query)
        has_english = any('a' <= char.lower() <= 'z' for char in query)
        return has_chinese and has_english
    
    async def _hierarchical_search(self, query: SearchQuery, 
                                 query_vectors: Dict[str, np.ndarray]) -> List[SearchResult]:
        """分层搜索"""
        try:
            # 1. 集群级搜索
            relevant_clusters = await self._search_clusters(query_vectors['primary'])
            
            # 2. 在相关集群内搜索
            cluster_results = []
            for cluster_id in relevant_clusters[:5]:  # 限制搜索的集群数量
                cluster_docs = [doc for doc in self.documents if doc.cluster_id == cluster_id]
                if cluster_docs:
                    results = await self._search_in_cluster(query, query_vectors, cluster_docs)
                    cluster_results.extend(results)
            
            # 3. 全局搜索补充
            if len(cluster_results) < query.limit:
                global_results = await self._flat_search(query, query_vectors)
                cluster_results.extend(global_results)
            
            return cluster_results
            
        except Exception as e:
            logger.error(f"Hierarchical search failed: {e}")
            return await self._flat_search(query, query_vectors)
    
    async def _search_clusters(self, query_vector: np.ndarray) -> List[int]:
        """搜索相关集群"""
        if not self.clusters:
            return list(range(min(self.cluster_count, len(set(doc.cluster_id for doc in self.documents if doc.cluster_id is not None)))))
        
        # 计算查询向量与集群中心的相似度
        cluster_similarities = []
        for cluster_id, cluster_info in self.clusters.items():
            if 'centroid' in cluster_info:
                similarity = cosine_similarity([query_vector], [cluster_info['centroid']])[0][0]
                cluster_similarities.append((cluster_id, similarity))
        
        # 按相似度排序
        cluster_similarities.sort(key=lambda x: x[1], reverse=True)
        return [cluster_id for cluster_id, _ in cluster_similarities]
    
    async def _search_in_cluster(self, query: SearchQuery, query_vectors: Dict[str, np.ndarray],
                               cluster_docs: List[VectorDocument]) -> List[SearchResult]:
        """在集群内搜索"""
        if not cluster_docs:
            return []
        
        # 构建集群内的向量矩阵
        doc_vectors = np.array([doc.vector for doc in cluster_docs])
        
        # 计算相似度
        similarities = cosine_similarity([query_vectors['primary']], doc_vectors)[0]
        
        # 创建结果
        results = []
        for i, (doc, similarity) in enumerate(zip(cluster_docs, similarities)):
            if similarity >= query.min_score:
                result = SearchResult(
                    id=doc.id,
                    content=doc.content,
                    score=float(similarity),
                    metadata={**doc.metadata, 'cluster_id': doc.cluster_id},
                    source=self.name,
                    search_type=SearchType.SEMANTIC
                )
                results.append(result)
        
        # 按相似度排序
        results.sort(key=lambda x: x.score, reverse=True)
        return results
    
    async def _flat_search(self, query: SearchQuery, 
                          query_vectors: Dict[str, np.ndarray]) -> List[SearchResult]:
        """平面搜索"""
        if not self.documents:
            return []
        
        try:
            # 使用FAISS进行快速搜索
            query_vector = query_vectors['primary'].reshape(1, -1)
            
            # 确保索引已训练
            if hasattr(self.faiss_index, 'is_trained') and not self.faiss_index.is_trained:
                if len(self.documents) >= self.faiss_index.nlist:
                    vectors = np.array([doc.vector for doc in self.documents])
                    self.faiss_index.train(vectors)
            
            # 搜索
            k = min(query.limit * 2, len(self.documents))
            scores, indices = self.faiss_index.search(query_vector, k)
            
            # 构建结果
            results = []
            for score, idx in zip(scores[0], indices[0]):
                if idx >= 0 and idx < len(self.documents) and score >= query.min_score:
                    doc = self.documents[idx]
                    result = SearchResult(
                        id=doc.id,
                        content=doc.content,
                        score=float(score),
                        metadata=doc.metadata,
                        source=self.name,
                        search_type=SearchType.SEMANTIC
                    )
                    results.append(result)
            
            return results
            
        except Exception as e:
            logger.error(f"FAISS search failed, falling back to numpy: {e}")
            return await self._numpy_search(query, query_vectors)
    
    async def _numpy_search(self, query: SearchQuery, 
                           query_vectors: Dict[str, np.ndarray]) -> List[SearchResult]:
        """NumPy后备搜索"""
        if not self.documents:
            return []
        
        # 构建文档向量矩阵
        doc_vectors = np.array([doc.vector for doc in self.documents])
        
        # 计算相似度
        similarities = cosine_similarity([query_vectors['primary']], doc_vectors)[0]
        
        # 获取top-k结果
        top_indices = np.argsort(similarities)[::-1][:query.limit * 2]
        
        results = []
        for idx in top_indices:
            if similarities[idx] >= query.min_score:
                doc = self.documents[idx]
                result = SearchResult(
                    id=doc.id,
                    content=doc.content,
                    score=float(similarities[idx]),
                    metadata=doc.metadata,
                    source=self.name,
                    search_type=SearchType.SEMANTIC
                )
                results.append(result)
        
        return results
    
    def _post_process_results(self, results: List[SearchResult], 
                            query: SearchQuery) -> List[SearchResult]:
        """后处理搜索结果"""
        if not results:
            return results
        
        # 去重
        seen_ids = set()
        unique_results = []
        for result in results:
            if result.id not in seen_ids:
                seen_ids.add(result.id)
                unique_results.append(result)
        
        # 应用筛选条件
        if query.filters:
            filtered_results = []
            for result in unique_results:
                if self._apply_filters(result.metadata, query.filters):
                    filtered_results.append(result)
            unique_results = filtered_results
        
        # 按分数排序
        unique_results.sort(key=lambda x: x.score, reverse=True)
        
        return unique_results
    
    async def add_documents(self, documents: List[Dict[str, Any]]) -> bool:
        """添加文档"""
        if not self.connected:
            return False
        
        try:
            new_docs = []
            vectors_to_add = []
            
            for doc_data in documents:
                # 生成向量
                content = doc_data.get('content', '')
                if not content.strip():
                    continue
                
                # 使用主要模型生成向量
                vector = self.primary_model.encode([content], normalize_embeddings=True)[0]
                
                # 创建向量文档
                vector_doc = VectorDocument(
                    id=doc_data.get('id', f"doc_{len(self.documents)}"),
                    content=content,
                    vector=vector,
                    metadata=doc_data.get('metadata', {})
                )
                
                new_docs.append(vector_doc)
                vectors_to_add.append(vector)
            
            if not new_docs:
                return False
            
            # 添加到FAISS索引
            if vectors_to_add:
                vectors_array = np.array(vectors_to_add)
                self.faiss_index.add(vectors_array)
            
            # 添加到文档列表
            self.documents.extend(new_docs)
            
            # 如果启用分层索引且文档数量足够，重新聚类
            if (self.enable_hierarchical_index and 
                len(self.documents) > 100 and 
                len(self.documents) % 100 == 0):
                await self._update_clusters()
            
            logger.info(f"Added {len(new_docs)} documents to advanced vector store")
            return True
            
        except Exception as e:
            logger.error(f"Failed to add documents: {e}")
            return False
    
    async def _update_clusters(self):
        """更新文档聚类"""
        try:
            if len(self.documents) < self.cluster_count:
                return
            
            # 提取所有向量
            vectors = np.array([doc.vector for doc in self.documents])
            
            # K-means聚类
            n_clusters = min(self.cluster_count, len(self.documents) // 10)
            kmeans = KMeans(n_clusters=n_clusters, random_state=42, n_init=10)
            cluster_labels = kmeans.fit_predict(vectors)
            
            # 更新文档的集群ID
            for doc, cluster_id in zip(self.documents, cluster_labels):
                doc.cluster_id = int(cluster_id)
            
            # 保存集群信息
            self.clusters = {}
            for i in range(n_clusters):
                cluster_docs = [doc for doc in self.documents if doc.cluster_id == i]
                if cluster_docs:
                    cluster_vectors = np.array([doc.vector for doc in cluster_docs])
                    centroid = np.mean(cluster_vectors, axis=0)
                    
                    self.clusters[i] = {
                        'centroid': centroid,
                        'doc_count': len(cluster_docs),
                        'representative_docs': cluster_docs[:3]  # 保存代表性文档
                    }
            
            logger.info(f"Updated clusters: {len(self.clusters)} clusters for {len(self.documents)} documents")
            
        except Exception as e:
            logger.error(f"Cluster update failed: {e}")
    
    async def health_check(self) -> bool:
        """健康检查"""
        return (self.connected and 
                self.primary_model is not None and 
                self.faiss_index is not None)
    
    def get_statistics(self) -> Dict[str, Any]:
        """获取统计信息"""
        return {
            'total_documents': len(self.documents),
            'index_size': self.faiss_index.ntotal if self.faiss_index else 0,
            'clusters': len(self.clusters),
            'dimension': self.dimension,
            'models': {
                'primary': self.primary_model_name,
                'secondary': self.secondary_model_name
            }
        }
