"""
LLM增强的搜索管理器
"""
import logging
import asyncio
from typing import List, Dict, Any, Optional

from ..core.search_manager import SearchManager
from ..core.interfaces import SearchQuery, SearchResult, SearchType
from .document_processor import LLMDocumentProcessor, DocumentChunk
from .query_processor import LLMQueryProcessor, QueryAnalysis
from .result_optimizer import LLMResultOptimizer, OptimizedResult

logger = logging.getLogger(__name__)


class EnhancedSearchManager(SearchManager):
    """LLM增强的搜索管理器"""
    
    def __init__(self, config: Dict[str, Any]):
        super().__init__(config)
        
        # LLM组件配置
        llm_config = config.get('llm', {})
        
        # 初始化LLM组件
        self.document_processor = LLMDocumentProcessor(llm_config.get('document_processor', {}))
        self.query_processor = LLMQueryProcessor(llm_config.get('query_processor', {}))
        self.result_optimizer = LLMResultOptimizer(llm_config.get('result_optimizer', {}))
        
        # 功能开关
        self.enable_query_enhancement = llm_config.get('enable_query_enhancement', True)
        self.enable_result_optimization = llm_config.get('enable_result_optimization', True)
        self.enable_answer_generation = llm_config.get('enable_answer_generation', True)
        
        # 缓存
        self.query_cache = {}
        self.result_cache = {}
    
    async def enhanced_search(self, query: SearchQuery) -> Dict[str, Any]:
        """增强搜索功能"""
        try:
            # 1. 查询理解和扩展
            query_analysis = None
            if self.enable_query_enhancement:
                query_analysis = await self.query_processor.analyze_query(query.query)
                logger.info(f"Query analysis: intent={query_analysis.intent}, "
                          f"keywords={query_analysis.keywords}")
            
            # 2. 执行多种搜索策略
            search_results = await self._execute_enhanced_search(query, query_analysis)
            
            # 3. 结果优化和重排序
            optimized_results = []
            if self.enable_result_optimization and search_results:
                optimized_results = await self.result_optimizer.optimize_results(
                    search_results, query
                )
            else:
                # 创建默认优化结果
                optimized_results = [
                    OptimizedResult(
                        original_result=result,
                        relevance_score=0.8,
                        quality_score=0.8,
                        final_score=result.score,
                        explanation="未启用LLM优化"
                    ) for result in search_results
                ]
            
            # 4. 生成答案
            generated_answer = None
            if self.enable_answer_generation and optimized_results:
                generated_answer = await self.result_optimizer.generate_answer(
                    optimized_results, query.query
                )
            
            # 5. 构建响应
            response = {
                'query': query.query,
                'query_analysis': query_analysis,
                'results': [opt_result.original_result for opt_result in optimized_results],
                'optimized_results': optimized_results,
                'generated_answer': generated_answer,
                'total_results': len(optimized_results),
                'search_time': 0  # 可以添加计时
            }
            
            return response
            
        except Exception as e:
            logger.error(f"Enhanced search failed: {e}")
            # 回退到基础搜索
            basic_results = await self.search(query)
            return {
                'query': query.query,
                'results': basic_results,
                'total_results': len(basic_results),
                'error': str(e)
            }
    
    async def _execute_enhanced_search(self, query: SearchQuery, 
                                     query_analysis: Optional[QueryAnalysis]) -> List[SearchResult]:
        """执行增强搜索"""
        all_results = []
        
        try:
            # 基础搜索
            base_results = await self.search(query)
            all_results.extend(base_results)
            
            # 如果有查询分析，执行扩展搜索
            if query_analysis and query_analysis.expanded_queries:
                expanded_tasks = []
                
                for expanded_query in query_analysis.expanded_queries[:3]:  # 限制扩展查询数量
                    if expanded_query != query.query:  # 避免重复搜索
                        expanded_search_query = SearchQuery(
                            query=expanded_query,
                            search_type=query.search_type,
                            filters=query.filters,
                            limit=query.limit // 2,  # 减少每个扩展查询的结果数
                            offset=query.offset,
                            boost_fields=query.boost_fields,
                            min_score=query.min_score
                        )
                        expanded_tasks.append(self.search(expanded_search_query))
                
                if expanded_tasks:
                    expanded_results = await asyncio.gather(*expanded_tasks, return_exceptions=True)
                    
                    for result in expanded_results:
                        if isinstance(result, list):
                            all_results.extend(result)
                        else:
                            logger.warning(f"Expanded search failed: {result}")
            
            # 去重和限制结果数量
            unique_results = self._deduplicate_results(all_results)
            return unique_results[:query.limit * 2]  # 返回更多结果用于后续优化
            
        except Exception as e:
            logger.error(f"Enhanced search execution failed: {e}")
            return all_results
    
    async def add_documents_with_processing(self, documents: List[Dict[str, Any]]) -> bool:
        """添加文档并进行LLM预处理"""
        try:
            # 使用LLM处理文档
            processed_chunks = await self.document_processor.batch_process_documents(documents)
            
            if not processed_chunks:
                logger.warning("No chunks generated from documents")
                return False
            
            # 将处理后的块转换为标准文档格式
            enhanced_documents = []
            for chunk in processed_chunks:
                doc = {
                    'id': chunk.chunk_id,
                    'content': chunk.content,
                    'metadata': {
                        **chunk.metadata,
                        'parent_doc_id': chunk.parent_doc_id,
                        'chunk_index': chunk.chunk_index,
                        'summary': chunk.summary,
                        'keywords': chunk.keywords,
                        'semantic_tags': chunk.semantic_tags
                    }
                }
                enhanced_documents.append(doc)
            
            # 添加到各个数据源
            success_count = 0
            for data_source in self.data_sources:
                if hasattr(data_source, 'add_documents'):
                    try:
                        if await data_source.add_documents(enhanced_documents):
                            success_count += 1
                    except Exception as e:
                        logger.error(f"Failed to add documents to {data_source.name}: {e}")
            
            logger.info(f"Added {len(enhanced_documents)} processed chunks to "
                       f"{success_count} data sources")
            return success_count > 0
            
        except Exception as e:
            logger.error(f"Document processing and addition failed: {e}")
            return False
    
    async def get_search_suggestions(self, partial_query: str) -> List[str]:
        """获取搜索建议"""
        try:
            if len(partial_query) < 2:
                return []
            
            # 使用LLM生成搜索建议
            suggestions_prompt = f"""基于部分查询"{partial_query}"，生成5个相关的搜索建议：

部分查询：{partial_query}

搜索建议（每行一个）："""
            
            response = await self.query_processor.llm.ainvoke(suggestions_prompt)
            
            suggestions = []
            for line in response.content.strip().split('\n'):
                line = line.strip()
                if line and not line.startswith('搜索建议'):
                    # 移除序号
                    import re
                    line = re.sub(r'^\d+\.\s*', '', line)
                    suggestions.append(line)
            
            return suggestions[:5]
            
        except Exception as e:
            logger.error(f"Search suggestions generation failed: {e}")
            return []
    
    async def explain_search_results(self, query: str, results: List[SearchResult]) -> str:
        """解释搜索结果"""
        try:
            if not results:
                return "没有找到相关结果。"
            
            explanation_prompt = f"""请解释为什么这些搜索结果与查询相关：

查询：{query}

结果数量：{len(results)}
主要来源：{', '.join(set(r.source for r in results[:3]))}

请生成一个简短的解释，说明：
1. 找到了什么类型的信息
2. 结果的相关性
3. 可能的后续搜索建议

解释："""
            
            response = await self.query_processor.llm.ainvoke(explanation_prompt)
            return response.content.strip()
            
        except Exception as e:
            logger.error(f"Search results explanation failed: {e}")
            return "搜索结果解释生成失败。"
    
    def _deduplicate_results(self, results: List[SearchResult]) -> List[SearchResult]:
        """去重搜索结果（增强版）"""
        if not results:
            return results
        
        seen_contents = set()
        unique_results = []
        
        for result in results:
            # 使用内容的前100个字符作为去重标识
            content_key = result.content[:100].strip()
            
            if content_key not in seen_contents:
                seen_contents.add(content_key)
                unique_results.append(result)
        
        return unique_results
    
    async def get_analytics(self) -> Dict[str, Any]:
        """获取搜索分析数据"""
        return {
            'total_queries': len(self.query_cache),
            'cache_hit_rate': 0.0,  # 可以实现缓存命中率统计
            'average_results_per_query': 0.0,  # 可以实现平均结果数统计
            'most_common_intents': [],  # 可以实现意图统计
            'data_sources_status': [
                {
                    'name': ds.name,
                    'type': ds.source_type,
                    'healthy': await ds.health_check()
                } for ds in self.data_sources
            ]
        }
