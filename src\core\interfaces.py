"""
混合检索系统的核心接口定义
"""
from abc import ABC, abstractmethod
from typing import List, Dict, Any, Optional, Union
from dataclasses import dataclass
from enum import Enum


class SearchType(Enum):
    """检索类型枚举"""
    KEYWORD = "keyword"
    SEMANTIC = "semantic"
    HYBRID = "hybrid"


class DataSourceType(Enum):
    """数据源类型枚举"""
    ELASTICSEARCH = "elasticsearch"
    VECTOR_DB = "vector_db"
    RELATIONAL_DB = "relational_db"
    MONGODB = "mongodb"
    REDIS = "redis"


@dataclass
class SearchResult:
    """检索结果数据结构"""
    id: str
    content: str
    score: float
    metadata: Dict[str, Any]
    source: str
    search_type: SearchType


@dataclass
class SearchFilter:
    """结构化数据筛选条件"""
    field: str
    operator: str  # eq, ne, gt, lt, gte, lte, in, not_in, contains
    value: Union[str, int, float, List[Any]]


@dataclass
class SearchQuery:
    """检索查询参数"""
    query: str
    search_type: SearchType = SearchType.HYBRID
    filters: List[SearchFilter] = None
    limit: int = 10
    offset: int = 0
    boost_fields: Dict[str, float] = None
    min_score: float = 0.0


class DataSource(ABC):
    """数据源抽象接口"""
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.source_type = config.get('type')
        self.name = config.get('name', 'unknown')
    
    @abstractmethod
    async def connect(self) -> bool:
        """连接数据源"""
        pass
    
    @abstractmethod
    async def disconnect(self) -> None:
        """断开数据源连接"""
        pass
    
    @abstractmethod
    async def search(self, query: SearchQuery) -> List[SearchResult]:
        """执行检索"""
        pass
    
    @abstractmethod
    async def health_check(self) -> bool:
        """健康检查"""
        pass


class SearchStrategy(ABC):
    """检索策略抽象接口"""
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.strategy_type = config.get('type')
    
    @abstractmethod
    async def search(self, data_sources: List[DataSource], query: SearchQuery) -> List[SearchResult]:
        """执行检索策略"""
        pass
    
    @abstractmethod
    def merge_results(self, results: List[List[SearchResult]]) -> List[SearchResult]:
        """合并多个数据源的检索结果"""
        pass


class ResultRanker(ABC):
    """结果排序器抽象接口"""
    
    @abstractmethod
    def rank(self, results: List[SearchResult], query: SearchQuery) -> List[SearchResult]:
        """对检索结果进行排序"""
        pass


class FilterProcessor(ABC):
    """筛选处理器抽象接口"""
    
    @abstractmethod
    def apply_filters(self, query: SearchQuery, data_source_query: Dict[str, Any]) -> Dict[str, Any]:
        """应用筛选条件到数据源查询"""
        pass
